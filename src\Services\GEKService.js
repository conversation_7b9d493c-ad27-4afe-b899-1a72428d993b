import api from './ApiService';

/**
 * GEK (General Estimating Knowledge) Service
 * Handles all GEK-related API calls for employee performance analysis and task assignment
 */
class GEKService {
  /**
   * Get employee recommendations for a specific task
   * @param {string} taskId - The task ID to get recommendations for
   * @returns {Promise} API response with ranked employees
   */
  static async getTaskEmployeeMatches(taskId) {
    try {
      // First try to get real data from backend
      const response = await api.get(`/api/gek/task-matches/${taskId}`);
      return response.data;
    } catch (error) {
      console.error('Error getting task employee matches from API:', error);

      // Fallback: Get real users and generate matches
      try {
        const usersResponse = await api.get('/api/users');
        const users = usersResponse.data.users || usersResponse.data;

        // Filter out HR users and get only regular employees
        const employees = users.filter(user => user.role !== 'hr');

        return this.generateRealEmployeeMatches(taskId, employees);
      } catch (userError) {
        console.error('Error getting users for matches:', userError);
        // Final fallback: Return mock data
        return this.getMockEmployeeMatches(taskId);
      }
    }
  }

  /**
   * Reassign a task to a different employee
   * @param {string} taskId - The task ID
   * @param {string} employeeId - The employee ID to assign to
   * @param {string} reason - Reason for assignment
   * @returns {Promise} API response
   */
  static async reassignTask(taskId, employeeId, reason = '') {
    try {
      const response = await api.put(`/api/tasks/${taskId}`, {
        assignedTo: employeeId,
        status: 'Assigned',
        assignmentReason: reason
      });
      return response.data;
    } catch (error) {
      console.error('Error reassigning task:', error);
      throw error;
    }
  }

  /**
   * Calculate fit score for an employee and task combination
   * @param {string} employeeId - The employee ID
   * @param {string} category - Task category
   * @param {string} priority - Task priority
   * @returns {Promise} Fit score data
   */
  static async calculateFitScore(employeeId, category, priority) {
    try {
      const response = await api.post('/api/gek/calculate-fit', {
        employeeId,
        category,
        priority
      });
      return response.data;
    } catch (error) {
      console.error('Error calculating fit score:', error);
      
      // Fallback: Return mock fit score
      return this.getMockFitScore(employeeId, category, priority);
    }
  }

  /**
   * Get GEK analytics and insights using real data
   * @returns {Promise} GEK analytics data
   */
  static async getGEKAnalytics() {
    try {
      // Try to get real analytics from backend
      const response = await api.get('/api/gek/analytics');
      return response.data;
    } catch (error) {
      console.error('Error getting GEK analytics from API:', error);

      // Generate analytics from real data
      try {
        return await this.generateRealAnalytics();
      } catch (realDataError) {
        console.error('Error generating real analytics:', realDataError);
        // Final fallback: Return basic analytics
        return this.getBasicAnalytics();
      }
    }
  }

  /**
   * Get employee performance metrics using real data
   * @param {string} employeeId - The employee ID
   * @returns {Promise} Employee performance data
   */
  static async getEmployeePerformance(employeeId) {
    try {
      // Try to get real performance data from backend
      const response = await api.get(`/api/gek/employee/${employeeId}/performance`);
      return response.data;
    } catch (error) {
      console.error('Error getting employee performance from API:', error);

      // Generate performance data from real task data
      try {
        return await this.generateRealEmployeePerformance(employeeId);
      } catch (realDataError) {
        console.error('Error generating real employee performance:', realDataError);
        // Final fallback: Return basic performance data
        return this.getBasicEmployeePerformance(employeeId);
      }
    }
  }

  /**
   * Generate employee performance from real task data
   * @param {string} employeeId - Employee ID
   * @returns {Promise} Real performance data
   */
  static async generateRealEmployeePerformance(employeeId) {
    try {
      // Get employee's tasks
      const tasksResponse = await api.get(`/api/tasks/hr?assignedTo=${employeeId}&limit=100`);
      const tasks = tasksResponse.data.tasks || tasksResponse.data || [];

      // Get employee details
      const userResponse = await api.get(`/api/users/${employeeId}`);
      const user = userResponse.data;

      const completedTasks = tasks.filter(task => task.status === 'Completed');
      const tasksWithTime = completedTasks.filter(task => task.actualCompletionTime > 0);
      const tasksWithFitScore = tasks.filter(task => task.gekFitScore > 0);

      // Calculate metrics
      const tasksCompleted = completedTasks.length;
      const averageCompletionTime = tasksWithTime.length > 0
        ? tasksWithTime.reduce((sum, task) => sum + task.actualCompletionTime, 0) / tasksWithTime.length
        : 0;

      const averageFitScore = tasksWithFitScore.length > 0
        ? tasksWithFitScore.reduce((sum, task) => sum + task.gekFitScore, 0) / tasksWithFitScore.length
        : 75;

      // Calculate on-time delivery
      const onTimeDelivery = completedTasks.length > 0
        ? completedTasks.filter(task => {
            const deadline = new Date(task.deadline);
            const completed = new Date(task.completedAt || task.updatedAt);
            return completed <= deadline;
          }).length / completedTasks.length
        : 0.85;

      // Quality rating based on fit scores and completion rate
      const qualityRating = Math.min(5, (averageFitScore / 20) + (onTimeDelivery * 2));

      return {
        employeeId,
        employeeName: user.name,
        overallScore: Math.round(averageFitScore),
        tasksCompleted,
        averageCompletionTime: Math.round(averageCompletionTime * 10) / 10,
        onTimeDelivery: Math.round(onTimeDelivery * 100) / 100,
        qualityRating: Math.round(qualityRating * 10) / 10,
        skillAreas: [
          { skill: 'Task Completion', score: Math.min(100, tasksCompleted * 5) },
          { skill: 'Time Management', score: Math.round((1 / Math.max(0.1, averageCompletionTime / 8)) * 100) },
          { skill: 'Quality Delivery', score: Math.round(qualityRating * 20) },
          { skill: 'Reliability', score: Math.round(onTimeDelivery * 100) }
        ],
        recentTasks: completedTasks.slice(-3).map(task => ({
          title: task.title,
          score: task.gekFitScore || 75,
          completedIn: task.actualCompletionTime || 8,
          completedAt: task.completedAt
        })),
        lastUpdated: new Date().toISOString()
      };
    } catch (error) {
      console.error('Error generating real employee performance:', error);
      throw error;
    }
  }

  /**
   * Get basic employee performance when real data is not available
   * @param {string} employeeId - Employee ID
   * @returns {Object} Basic performance data
   */
  static getBasicEmployeePerformance(employeeId) {
    return {
      employeeId,
      employeeName: 'Unknown Employee',
      overallScore: 0,
      tasksCompleted: 0,
      averageCompletionTime: 0,
      onTimeDelivery: 0,
      qualityRating: 0,
      skillAreas: [
        { skill: 'Task Completion', score: 0 },
        { skill: 'Time Management', score: 0 },
        { skill: 'Quality Delivery', score: 0 },
        { skill: 'Reliability', score: 0 }
      ],
      recentTasks: [],
      lastUpdated: new Date().toISOString(),
      message: 'No performance data available yet. Complete some tasks to see metrics.'
    };
  }

  /**
   * Get ranked employees for task assignment
   * @param {Object} criteria - Task criteria (category, priority)
   * @param {Array} userIds - Specific user IDs (optional)
   * @param {number} limit - Number of results to return
   * @returns {Promise} Ranked employees data
   */
  static async getRankedEmployees(criteria, userIds = null, limit = 10) {
    try {
      // Get all users
      const usersResponse = await api.get('/api/users');
      const allUsers = (usersResponse.data.users || usersResponse.data || []).filter(user => user.role !== 'hr');

      // Filter by specific user IDs if provided
      const users = userIds ? allUsers.filter(user => userIds.includes(user._id)) : allUsers;

      if (users.length === 0) {
        return {
          rankedEmployees: [],
          totalCandidates: 0,
          criteria,
          message: 'No employees found matching the criteria.'
        };
      }

      // Create a mock task for scoring
      const mockTask = {
        category: criteria.category || 'General',
        priority: criteria.priority || 'Medium',
        title: `${criteria.category} Task`,
        description: `Sample ${criteria.category} task for ranking`
      };

      // Generate fit scores for each employee
      const rankedEmployees = users.map(user => {
        const fitScore = this.calculateEmployeeFitScore(user, mockTask);
        const estimatedTime = this.estimateCompletionTime(user, mockTask);

        return {
          user: {
            _id: user._id,
            name: user.name,
            email: user.email,
            job: user.job || 'Employee',
            department: user.department || 'General'
          },
          fitScore,
          estimatedCompletionTime: estimatedTime,
          confidence: fitScore / 100,
          reasons: this.generateFitReasons(user, mockTask, fitScore)
        };
      });

      // Sort by fit score and limit results
      rankedEmployees.sort((a, b) => b.fitScore - a.fitScore);

      return {
        rankedEmployees: rankedEmployees.slice(0, limit),
        totalCandidates: users.length,
        criteria,
        analysisTimestamp: new Date().toISOString()
      };
    } catch (error) {
      console.error('Error getting ranked employees:', error);
      throw error;
    }
  }

  /**
   * Get department ranking for specific department
   * @param {string} department - Department name
   * @param {string} category - Task category
   * @param {string} priority - Task priority
   * @param {number} limit - Number of results
   * @returns {Promise} Department ranking data
   */
  static async getDepartmentRanking(department, category, priority, limit = 10) {
    try {
      // Get users from specific department
      const usersResponse = await api.get('/api/users');
      const allUsers = (usersResponse.data.users || usersResponse.data || []).filter(user => user.role !== 'hr');
      const departmentUsers = allUsers.filter(user =>
        user.department && user.department.toLowerCase() === department.toLowerCase()
      );

      return await this.getRankedEmployees({ category, priority }, departmentUsers.map(u => u._id), limit);
    } catch (error) {
      console.error('Error getting department ranking:', error);
      throw error;
    }
  }

  /**
   * Get user estimates for all categories
   * @param {string} userId - User ID
   * @returns {Promise} User estimates data
   */
  static async getUserEstimates(userId) {
    try {
      // Try to get from backend first
      const response = await api.get(`/api/gek/user/${userId}/estimates`);
      return response.data;
    } catch (error) {
      console.error('Error getting user estimates from API:', error);

      // Generate estimates for all categories
      try {
        return await this.generateUserEstimates(userId);
      } catch (generateError) {
        console.error('Error generating user estimates:', generateError);
        return { estimates: [] };
      }
    }
  }

  /**
   * Generate user estimates for all task categories
   * @param {string} userId - User ID
   * @returns {Promise} Generated estimates
   */
  static async generateUserEstimates(userId) {
    try {
      const userResponse = await api.get(`/api/users/${userId}`);
      const user = userResponse.data;

      const categories = ['General', 'Development', 'Design', 'Marketing', 'HR', 'Finance', 'Operations'];
      const priorities = ['Low', 'Medium', 'High', 'Urgent'];

      const estimates = [];

      for (const category of categories) {
        for (const priority of priorities) {
          const mockTask = {
            category,
            priority,
            title: `${category} Task`,
            description: `Sample ${category} task`
          };

          const fitScore = this.calculateEmployeeFitScore(user, mockTask);
          const estimatedTime = this.estimateCompletionTime(user, mockTask);

          estimates.push({
            taskCategory: category,
            taskPriority: priority,
            fitScore,
            estimatedCompletionTime: estimatedTime,
            confidence: fitScore / 100,
            performanceMetrics: {
              completionRate: Math.min(1, fitScore / 100),
              averageTime: estimatedTime,
              qualityScore: fitScore / 100
            }
          });
        }
      }

      return {
        userId,
        estimates,
        lastUpdated: new Date().toISOString()
      };
    } catch (error) {
      console.error('Error generating user estimates:', error);
      throw error;
    }
  }

  /**
   * Get fit score for specific user and task criteria
   * @param {string} userId - User ID
   * @param {string} category - Task category
   * @param {string} priority - Task priority
   * @param {boolean} forceRecalculation - Force recalculation
   * @returns {Promise} Fit score data
   */
  static async getFitScore(userId, category, priority, forceRecalculation = false) {
    try {
      // Try to get from backend first
      if (!forceRecalculation) {
        const response = await api.get(`/api/gek/fit-score/${userId}?category=${category}&priority=${priority}`);
        return response.data;
      }
    } catch (error) {
      console.log('Backend fit score not available, generating...');
    }

    // Generate fit score
    try {
      const userResponse = await api.get(`/api/users/${userId}`);
      const user = userResponse.data;

      const mockTask = {
        category,
        priority,
        title: `${category} Task`,
        description: `Sample ${category} task`
      };

      const fitScore = this.calculateEmployeeFitScore(user, mockTask);
      const estimatedTime = this.estimateCompletionTime(user, mockTask);

      return {
        estimate: {
          taskCategory: category,
          taskPriority: priority,
          fitScore,
          estimatedCompletionTime: estimatedTime,
          confidence: fitScore / 100,
          performanceMetrics: {
            completionRate: Math.min(1, fitScore / 100),
            averageTime: estimatedTime,
            qualityScore: fitScore / 100
          }
        }
      };
    } catch (error) {
      console.error('Error calculating fit score:', error);
      throw error;
    }
  }

  /**
   * Generate employee matches using real user data
   * @param {string} taskId - Task ID
   * @param {Array} employees - Array of real employee data
   * @returns {Object} Employee matches with real data
   */
  static async generateRealEmployeeMatches(taskId, employees) {
    try {
      // Get task details to analyze
      const taskResponse = await api.get(`/api/tasks/${taskId}`);
      const task = taskResponse.data;

      // Generate fit scores for each employee
      const rankedEmployees = employees.map(employee => {
        const fitScore = this.calculateEmployeeFitScore(employee, task);
        const estimatedTime = this.estimateCompletionTime(employee, task);

        return {
          user: {
            _id: employee._id,
            name: employee.name,
            email: employee.email,
            job: employee.job || 'Employee',
            department: employee.department || 'General'
          },
          fitScore,
          estimatedCompletionTime: estimatedTime,
          confidence: fitScore / 100,
          reasons: this.generateFitReasons(employee, task, fitScore)
        };
      });

      // Sort by fit score (highest first)
      rankedEmployees.sort((a, b) => b.fitScore - a.fitScore);

      return {
        taskId,
        rankedEmployees,
        totalCandidates: rankedEmployees.length,
        analysisTimestamp: new Date().toISOString()
      };
    } catch (error) {
      console.error('Error generating real employee matches:', error);
      return this.getMockEmployeeMatches(taskId);
    }
  }

  /**
   * Calculate fit score for an employee and task
   * @param {Object} employee - Employee data
   * @param {Object} task - Task data
   * @returns {number} Fit score (0-100)
   */
  static calculateEmployeeFitScore(employee, task) {
    let score = 60; // Base score

    // Department match bonus
    const departmentMap = {
      'Development': ['Engineering', 'IT', 'Technology'],
      'Design': ['Design', 'Creative', 'UX'],
      'Marketing': ['Marketing', 'Sales', 'Communications'],
      'HR': ['HR', 'Human Resources', 'People'],
      'Finance': ['Finance', 'Accounting', 'Financial'],
      'Operations': ['Operations', 'Management', 'Admin']
    };

    const taskCategory = task.category || 'General';
    const employeeDept = employee.department || '';
    const relevantDepts = departmentMap[taskCategory] || [];

    if (relevantDepts.some(dept => employeeDept.toLowerCase().includes(dept.toLowerCase()))) {
      score += 15;
    }

    // Job title relevance
    const jobTitle = (employee.job || '').toLowerCase();
    const taskTitle = (task.title || '').toLowerCase();
    const taskDesc = (task.description || '').toLowerCase();

    if (taskCategory === 'Development' && (jobTitle.includes('developer') || jobTitle.includes('engineer'))) {
      score += 10;
    }
    if (taskCategory === 'Design' && (jobTitle.includes('designer') || jobTitle.includes('ux'))) {
      score += 10;
    }
    if (taskCategory === 'Marketing' && (jobTitle.includes('marketing') || jobTitle.includes('sales'))) {
      score += 10;
    }

    // Priority adjustment
    if (task.priority === 'Urgent' && jobTitle.includes('senior')) {
      score += 8;
    }
    if (task.priority === 'Low' && jobTitle.includes('junior')) {
      score += 5;
    }

    // Random factor for variation (simulating performance history)
    const randomFactor = Math.random() * 20 - 10; // -10 to +10
    score += randomFactor;

    return Math.max(30, Math.min(95, Math.round(score)));
  }

  /**
   * Estimate completion time for employee and task
   * @param {Object} employee - Employee data
   * @param {Object} task - Task data
   * @returns {number} Estimated hours
   */
  static estimateCompletionTime(employee, task) {
    const baseHours = {
      'Development': 16,
      'Design': 12,
      'Marketing': 8,
      'HR': 6,
      'Finance': 10,
      'Operations': 8,
      'General': 6
    };

    let hours = baseHours[task.category] || baseHours['General'];

    // Adjust based on priority
    if (task.priority === 'Urgent') hours *= 0.7;
    if (task.priority === 'Low') hours *= 1.3;

    // Adjust based on employee seniority
    const jobTitle = (employee.job || '').toLowerCase();
    if (jobTitle.includes('senior') || jobTitle.includes('lead')) {
      hours *= 0.8;
    }
    if (jobTitle.includes('junior')) {
      hours *= 1.2;
    }

    return Math.max(2, Math.round(hours));
  }

  /**
   * Generate reasons for fit score
   * @param {Object} employee - Employee data
   * @param {Object} task - Task data
   * @param {number} fitScore - Calculated fit score
   * @returns {Array} Array of reasons
   */
  static generateFitReasons(employee, task, fitScore) {
    const reasons = [];

    if (fitScore >= 80) {
      reasons.push('Excellent match for task requirements');
    }
    if (fitScore >= 70) {
      reasons.push('Strong relevant experience');
    }

    const jobTitle = (employee.job || '').toLowerCase();
    if (jobTitle.includes('senior')) {
      reasons.push('Senior-level expertise');
    }
    if (jobTitle.includes('lead')) {
      reasons.push('Leadership experience');
    }

    if (task.priority === 'Urgent' && fitScore >= 75) {
      reasons.push('Suitable for urgent tasks');
    }

    if (employee.department && task.category) {
      reasons.push(`${employee.department} department experience`);
    }

    if (reasons.length === 0) {
      reasons.push('Available for assignment');
    }

    return reasons.slice(0, 3); // Limit to 3 reasons
  }

  /**
   * Generate analytics from real data
   * @returns {Promise} Real analytics data
   */
  static async generateRealAnalytics() {
    try {
      // Get real users and tasks data
      const [usersResponse, tasksResponse] = await Promise.all([
        api.get('/api/users'),
        api.get('/api/tasks/hr?limit=1000') // Get more tasks for better analytics
      ]);

      const users = (usersResponse.data.users || usersResponse.data || []).filter(user => user.role !== 'hr');
      const tasks = tasksResponse.data.tasks || tasksResponse.data || [];

      // Calculate analytics from real data
      const totalTasks = tasks.length;
      const completedTasks = tasks.filter(task => task.status === 'Completed');
      const assignedTasks = tasks.filter(task => task.assignedTo);

      // Calculate average fit score from assigned tasks
      const tasksWithFitScore = assignedTasks.filter(task => task.gekFitScore > 0);
      const averageFitScore = tasksWithFitScore.length > 0
        ? tasksWithFitScore.reduce((sum, task) => sum + task.gekFitScore, 0) / tasksWithFitScore.length
        : 75; // Default if no fit scores

      // Calculate average completion time
      const tasksWithTime = completedTasks.filter(task => task.actualCompletionTime > 0);
      const averageCompletionTime = tasksWithTime.length > 0
        ? tasksWithTime.reduce((sum, task) => sum + task.actualCompletionTime, 0) / tasksWithTime.length
        : 8; // Default 8 hours

      // Calculate success rate (completed vs assigned)
      const successRate = assignedTasks.length > 0
        ? completedTasks.length / assignedTasks.length
        : 0.85; // Default 85%

      // Get top performers (users with most completed tasks)
      const userTaskCounts = {};
      completedTasks.forEach(task => {
        if (task.assignedTo) {
          const userId = task.assignedTo._id || task.assignedTo;
          userTaskCounts[userId] = (userTaskCounts[userId] || 0) + 1;
        }
      });

      const topPerformers = Object.entries(userTaskCounts)
        .map(([userId, taskCount]) => {
          const user = users.find(u => u._id === userId);
          return user ? {
            name: user.name,
            score: Math.min(95, 70 + taskCount * 2), // Estimate score based on task count
            tasksCompleted: taskCount
          } : null;
        })
        .filter(Boolean)
        .sort((a, b) => b.tasksCompleted - a.tasksCompleted)
        .slice(0, 5);

      // Calculate category performance
      const categoryPerformance = {};
      const categories = ['Development', 'Design', 'Marketing', 'HR', 'Finance', 'Operations', 'General'];

      categories.forEach(category => {
        const categoryTasks = tasks.filter(task => task.category === category);
        const categoryCompleted = categoryTasks.filter(task => task.status === 'Completed');
        const categoryWithFitScore = categoryTasks.filter(task => task.gekFitScore > 0);
        const categoryWithTime = categoryCompleted.filter(task => task.actualCompletionTime > 0);

        categoryPerformance[category] = {
          avgScore: categoryWithFitScore.length > 0
            ? categoryWithFitScore.reduce((sum, task) => sum + task.gekFitScore, 0) / categoryWithFitScore.length
            : 75,
          avgTime: categoryWithTime.length > 0
            ? categoryWithTime.reduce((sum, task) => sum + task.actualCompletionTime, 0) / categoryWithTime.length
            : 8
        };
      });

      return {
        totalTasks,
        totalUsers: users.length,
        averageFitScore: Math.round(averageFitScore * 10) / 10,
        averageCompletionTime: Math.round(averageCompletionTime * 10) / 10,
        successRate: Math.round(successRate * 100) / 100,
        topPerformers,
        categoryPerformance,
        trends: {
          fitScoreImprovement: 0.05, // Placeholder - would need historical data
          timeReduction: 0.03,
          accuracyIncrease: 0.08
        },
        lastUpdated: new Date().toISOString()
      };
    } catch (error) {
      console.error('Error generating real analytics:', error);
      throw error;
    }
  }

  /**
   * Get basic analytics when real data is not available
   * @returns {Object} Basic analytics
   */
  static getBasicAnalytics() {
    return {
      totalTasks: 0,
      totalUsers: 0,
      averageFitScore: 0,
      averageCompletionTime: 0,
      successRate: 0,
      topPerformers: [],
      categoryPerformance: {},
      trends: {
        fitScoreImprovement: 0,
        timeReduction: 0,
        accuracyIncrease: 0
      },
      lastUpdated: new Date().toISOString(),
      message: 'No data available yet. Create some tasks and assignments to see analytics.'
    };
  }

  // Mock data methods for development/fallback (keeping minimal for emergency use)

  /**
   * Fallback method when no real users are available
   * @param {string} taskId - Task ID
   * @returns {Object} Empty employee matches
   */
  static getMockEmployeeMatches(taskId) {
    return {
      taskId,
      rankedEmployees: [],
      totalCandidates: 0,
      analysisTimestamp: new Date().toISOString(),
      message: 'No employees found. Please ensure users are registered in the system.'
    };
  }

  /**
   * Generate mock fit score
   * @param {string} employeeId - Employee ID
   * @param {string} category - Task category
   * @param {string} priority - Task priority
   * @returns {Object} Mock fit score data
   */
  static getMockFitScore(employeeId, category, priority) {
    // Simple algorithm to generate consistent but varied scores
    const baseScore = 60 + (parseInt(employeeId.slice(-1)) || 1) * 5;
    const categoryBonus = category === 'Development' ? 10 : category === 'Design' ? 8 : 5;
    const priorityBonus = priority === 'High' ? 5 : priority === 'Urgent' ? 8 : 2;
    
    const fitScore = Math.min(95, baseScore + categoryBonus + priorityBonus);
    const estimatedCompletionTime = Math.max(4, 16 - (fitScore / 10));

    return {
      fitScore,
      estimatedCompletionTime: Math.round(estimatedCompletionTime),
      confidence: fitScore / 100,
      factors: {
        skillMatch: fitScore * 0.4,
        experienceLevel: fitScore * 0.3,
        availability: fitScore * 0.2,
        performanceHistory: fitScore * 0.1
      }
    };
  }

  /**
   * Generate mock analytics data
   * @returns {Object} Mock analytics
   */
  static getMockAnalytics() {
    return {
      totalTasks: 156,
      averageFitScore: 78.5,
      averageCompletionTime: 9.2,
      successRate: 0.89,
      topPerformers: [
        { name: 'John Smith', score: 92, tasksCompleted: 23 },
        { name: 'Sarah Johnson', score: 87, tasksCompleted: 19 },
        { name: 'Mike Chen', score: 82, tasksCompleted: 21 }
      ],
      categoryPerformance: {
        'Development': { avgScore: 85, avgTime: 8.5 },
        'Design': { avgScore: 82, avgTime: 10.2 },
        'Marketing': { avgScore: 78, avgTime: 7.8 },
        'Operations': { avgScore: 75, avgTime: 9.5 }
      },
      trends: {
        fitScoreImprovement: 0.12,
        timeReduction: 0.08,
        accuracyIncrease: 0.15
      }
    };
  }

  /**
   * Generate mock employee performance data
   * @param {string} employeeId - Employee ID
   * @returns {Object} Mock performance data
   */
  static getMockEmployeePerformance(employeeId) {
    const basePerformance = 70 + (parseInt(employeeId.slice(-1)) || 1) * 3;
    
    return {
      employeeId,
      overallScore: basePerformance,
      tasksCompleted: 15 + Math.floor(Math.random() * 20),
      averageCompletionTime: 8 + Math.random() * 4,
      onTimeDelivery: 0.85 + Math.random() * 0.1,
      qualityRating: 4.2 + Math.random() * 0.6,
      skillAreas: [
        { skill: 'Technical Skills', score: basePerformance + 5 },
        { skill: 'Communication', score: basePerformance - 2 },
        { skill: 'Problem Solving', score: basePerformance + 3 },
        { skill: 'Time Management', score: basePerformance }
      ],
      recentTasks: [
        { title: 'Feature Implementation', score: basePerformance + 8, completedIn: 6 },
        { title: 'Bug Fixes', score: basePerformance + 5, completedIn: 4 },
        { title: 'Code Review', score: basePerformance + 2, completedIn: 2 }
      ]
    };
  }
}

export default GEKService;
