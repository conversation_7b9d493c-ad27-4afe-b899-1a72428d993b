import api from './ApiService';

/**
 * GEK (General Estimating Knowledge) Service
 * Handles all GEK-related API calls for employee performance analysis and task assignment
 */
class GEKService {
  /**
   * Get employee recommendations for a specific task
   * @param {string} taskId - The task ID to get recommendations for
   * @returns {Promise} API response with ranked employees
   */
  static async getTaskEmployeeMatches(taskId) {
    try {
      // First try to get real data from backend
      const response = await api.get(`/api/gek/task-matches/${taskId}`);
      return response.data;
    } catch (error) {
      console.error('Error getting task employee matches from API:', error);

      // Fallback: Get real users and generate matches
      try {
        const usersResponse = await api.get('/api/users');
        const users = usersResponse.data.users || usersResponse.data;

        // Filter out HR users and get only regular employees
        const employees = users.filter(user => user.role !== 'hr');

        return this.generateRealEmployeeMatches(taskId, employees);
      } catch (userError) {
        console.error('Error getting users for matches:', userError);
        // Final fallback: Return mock data
        return this.getMockEmployeeMatches(taskId);
      }
    }
  }

  /**
   * Reassign a task to a different employee
   * @param {string} taskId - The task ID
   * @param {string} employeeId - The employee ID to assign to
   * @param {string} reason - Reason for assignment
   * @returns {Promise} API response
   */
  static async reassignTask(taskId, employeeId, reason = '') {
    try {
      const response = await api.put(`/api/tasks/${taskId}`, {
        assignedTo: employeeId,
        status: 'Assigned',
        assignmentReason: reason
      });
      return response.data;
    } catch (error) {
      console.error('Error reassigning task:', error);
      throw error;
    }
  }

  /**
   * Calculate fit score for an employee and task combination
   * @param {string} employeeId - The employee ID
   * @param {string} category - Task category
   * @param {string} priority - Task priority
   * @returns {Promise} Fit score data
   */
  static async calculateFitScore(employeeId, category, priority) {
    try {
      const response = await api.post('/api/gek/calculate-fit', {
        employeeId,
        category,
        priority
      });
      return response.data;
    } catch (error) {
      console.error('Error calculating fit score:', error);
      
      // Fallback: Return mock fit score
      return this.getMockFitScore(employeeId, category, priority);
    }
  }

  /**
   * Get GEK analytics and insights
   * @returns {Promise} GEK analytics data
   */
  static async getGEKAnalytics() {
    try {
      const response = await api.get('/api/gek/analytics');
      return response.data;
    } catch (error) {
      console.error('Error getting GEK analytics:', error);
      
      // Fallback: Return mock analytics
      return this.getMockAnalytics();
    }
  }

  /**
   * Get employee performance metrics
   * @param {string} employeeId - The employee ID
   * @returns {Promise} Employee performance data
   */
  static async getEmployeePerformance(employeeId) {
    try {
      const response = await api.get(`/api/gek/employee/${employeeId}/performance`);
      return response.data;
    } catch (error) {
      console.error('Error getting employee performance:', error);
      
      // Fallback: Return mock performance data
      return this.getMockEmployeePerformance(employeeId);
    }
  }

  /**
   * Generate employee matches using real user data
   * @param {string} taskId - Task ID
   * @param {Array} employees - Array of real employee data
   * @returns {Object} Employee matches with real data
   */
  static async generateRealEmployeeMatches(taskId, employees) {
    try {
      // Get task details to analyze
      const taskResponse = await api.get(`/api/tasks/${taskId}`);
      const task = taskResponse.data;

      // Generate fit scores for each employee
      const rankedEmployees = employees.map(employee => {
        const fitScore = this.calculateEmployeeFitScore(employee, task);
        const estimatedTime = this.estimateCompletionTime(employee, task);

        return {
          user: {
            _id: employee._id,
            name: employee.name,
            email: employee.email,
            job: employee.job || 'Employee',
            department: employee.department || 'General'
          },
          fitScore,
          estimatedCompletionTime: estimatedTime,
          confidence: fitScore / 100,
          reasons: this.generateFitReasons(employee, task, fitScore)
        };
      });

      // Sort by fit score (highest first)
      rankedEmployees.sort((a, b) => b.fitScore - a.fitScore);

      return {
        taskId,
        rankedEmployees,
        totalCandidates: rankedEmployees.length,
        analysisTimestamp: new Date().toISOString()
      };
    } catch (error) {
      console.error('Error generating real employee matches:', error);
      return this.getMockEmployeeMatches(taskId);
    }
  }

  /**
   * Calculate fit score for an employee and task
   * @param {Object} employee - Employee data
   * @param {Object} task - Task data
   * @returns {number} Fit score (0-100)
   */
  static calculateEmployeeFitScore(employee, task) {
    let score = 60; // Base score

    // Department match bonus
    const departmentMap = {
      'Development': ['Engineering', 'IT', 'Technology'],
      'Design': ['Design', 'Creative', 'UX'],
      'Marketing': ['Marketing', 'Sales', 'Communications'],
      'HR': ['HR', 'Human Resources', 'People'],
      'Finance': ['Finance', 'Accounting', 'Financial'],
      'Operations': ['Operations', 'Management', 'Admin']
    };

    const taskCategory = task.category || 'General';
    const employeeDept = employee.department || '';
    const relevantDepts = departmentMap[taskCategory] || [];

    if (relevantDepts.some(dept => employeeDept.toLowerCase().includes(dept.toLowerCase()))) {
      score += 15;
    }

    // Job title relevance
    const jobTitle = (employee.job || '').toLowerCase();
    const taskTitle = (task.title || '').toLowerCase();
    const taskDesc = (task.description || '').toLowerCase();

    if (taskCategory === 'Development' && (jobTitle.includes('developer') || jobTitle.includes('engineer'))) {
      score += 10;
    }
    if (taskCategory === 'Design' && (jobTitle.includes('designer') || jobTitle.includes('ux'))) {
      score += 10;
    }
    if (taskCategory === 'Marketing' && (jobTitle.includes('marketing') || jobTitle.includes('sales'))) {
      score += 10;
    }

    // Priority adjustment
    if (task.priority === 'Urgent' && jobTitle.includes('senior')) {
      score += 8;
    }
    if (task.priority === 'Low' && jobTitle.includes('junior')) {
      score += 5;
    }

    // Random factor for variation (simulating performance history)
    const randomFactor = Math.random() * 20 - 10; // -10 to +10
    score += randomFactor;

    return Math.max(30, Math.min(95, Math.round(score)));
  }

  /**
   * Estimate completion time for employee and task
   * @param {Object} employee - Employee data
   * @param {Object} task - Task data
   * @returns {number} Estimated hours
   */
  static estimateCompletionTime(employee, task) {
    const baseHours = {
      'Development': 16,
      'Design': 12,
      'Marketing': 8,
      'HR': 6,
      'Finance': 10,
      'Operations': 8,
      'General': 6
    };

    let hours = baseHours[task.category] || baseHours['General'];

    // Adjust based on priority
    if (task.priority === 'Urgent') hours *= 0.7;
    if (task.priority === 'Low') hours *= 1.3;

    // Adjust based on employee seniority
    const jobTitle = (employee.job || '').toLowerCase();
    if (jobTitle.includes('senior') || jobTitle.includes('lead')) {
      hours *= 0.8;
    }
    if (jobTitle.includes('junior')) {
      hours *= 1.2;
    }

    return Math.max(2, Math.round(hours));
  }

  /**
   * Generate reasons for fit score
   * @param {Object} employee - Employee data
   * @param {Object} task - Task data
   * @param {number} fitScore - Calculated fit score
   * @returns {Array} Array of reasons
   */
  static generateFitReasons(employee, task, fitScore) {
    const reasons = [];

    if (fitScore >= 80) {
      reasons.push('Excellent match for task requirements');
    }
    if (fitScore >= 70) {
      reasons.push('Strong relevant experience');
    }

    const jobTitle = (employee.job || '').toLowerCase();
    if (jobTitle.includes('senior')) {
      reasons.push('Senior-level expertise');
    }
    if (jobTitle.includes('lead')) {
      reasons.push('Leadership experience');
    }

    if (task.priority === 'Urgent' && fitScore >= 75) {
      reasons.push('Suitable for urgent tasks');
    }

    if (employee.department && task.category) {
      reasons.push(`${employee.department} department experience`);
    }

    if (reasons.length === 0) {
      reasons.push('Available for assignment');
    }

    return reasons.slice(0, 3); // Limit to 3 reasons
  }

  // Mock data methods for development/fallback

  /**
   * Generate mock employee matches for a task
   * @param {string} taskId - Task ID
   * @returns {Object} Mock employee matches
   */
  static getMockEmployeeMatches(taskId) {
    const mockEmployees = [
      {
        user: {
          _id: '1',
          name: 'John Smith',
          email: '<EMAIL>',
          job: 'Senior Developer',
          department: 'Engineering'
        },
        fitScore: 92,
        estimatedCompletionTime: 6,
        confidence: 0.95,
        reasons: ['Strong technical background', 'Previous similar tasks completed successfully', 'Available capacity']
      },
      {
        user: {
          _id: '2',
          name: 'Sarah Johnson',
          email: '<EMAIL>',
          job: 'Frontend Developer',
          department: 'Engineering'
        },
        fitScore: 87,
        estimatedCompletionTime: 8,
        confidence: 0.88,
        reasons: ['Relevant skill set', 'Good performance history', 'Team collaboration skills']
      },
      {
        user: {
          _id: '3',
          name: 'Mike Chen',
          email: '<EMAIL>',
          job: 'Full Stack Developer',
          department: 'Engineering'
        },
        fitScore: 82,
        estimatedCompletionTime: 10,
        confidence: 0.82,
        reasons: ['Versatile skill set', 'Reliable delivery', 'Cross-functional experience']
      },
      {
        user: {
          _id: '4',
          name: 'Emily Davis',
          email: '<EMAIL>',
          job: 'UI/UX Designer',
          department: 'Design'
        },
        fitScore: 75,
        estimatedCompletionTime: 12,
        confidence: 0.75,
        reasons: ['Creative problem solving', 'User-focused approach', 'Design thinking skills']
      },
      {
        user: {
          _id: '5',
          name: 'Alex Rodriguez',
          email: '<EMAIL>',
          job: 'Project Manager',
          department: 'Operations'
        },
        fitScore: 68,
        estimatedCompletionTime: 14,
        confidence: 0.68,
        reasons: ['Project coordination skills', 'Stakeholder management', 'Process optimization']
      }
    ];

    return {
      taskId,
      rankedEmployees: mockEmployees,
      totalCandidates: mockEmployees.length,
      analysisTimestamp: new Date().toISOString()
    };
  }

  /**
   * Generate mock fit score
   * @param {string} employeeId - Employee ID
   * @param {string} category - Task category
   * @param {string} priority - Task priority
   * @returns {Object} Mock fit score data
   */
  static getMockFitScore(employeeId, category, priority) {
    // Simple algorithm to generate consistent but varied scores
    const baseScore = 60 + (parseInt(employeeId.slice(-1)) || 1) * 5;
    const categoryBonus = category === 'Development' ? 10 : category === 'Design' ? 8 : 5;
    const priorityBonus = priority === 'High' ? 5 : priority === 'Urgent' ? 8 : 2;
    
    const fitScore = Math.min(95, baseScore + categoryBonus + priorityBonus);
    const estimatedCompletionTime = Math.max(4, 16 - (fitScore / 10));

    return {
      fitScore,
      estimatedCompletionTime: Math.round(estimatedCompletionTime),
      confidence: fitScore / 100,
      factors: {
        skillMatch: fitScore * 0.4,
        experienceLevel: fitScore * 0.3,
        availability: fitScore * 0.2,
        performanceHistory: fitScore * 0.1
      }
    };
  }

  /**
   * Generate mock analytics data
   * @returns {Object} Mock analytics
   */
  static getMockAnalytics() {
    return {
      totalTasks: 156,
      averageFitScore: 78.5,
      averageCompletionTime: 9.2,
      successRate: 0.89,
      topPerformers: [
        { name: 'John Smith', score: 92, tasksCompleted: 23 },
        { name: 'Sarah Johnson', score: 87, tasksCompleted: 19 },
        { name: 'Mike Chen', score: 82, tasksCompleted: 21 }
      ],
      categoryPerformance: {
        'Development': { avgScore: 85, avgTime: 8.5 },
        'Design': { avgScore: 82, avgTime: 10.2 },
        'Marketing': { avgScore: 78, avgTime: 7.8 },
        'Operations': { avgScore: 75, avgTime: 9.5 }
      },
      trends: {
        fitScoreImprovement: 0.12,
        timeReduction: 0.08,
        accuracyIncrease: 0.15
      }
    };
  }

  /**
   * Generate mock employee performance data
   * @param {string} employeeId - Employee ID
   * @returns {Object} Mock performance data
   */
  static getMockEmployeePerformance(employeeId) {
    const basePerformance = 70 + (parseInt(employeeId.slice(-1)) || 1) * 3;
    
    return {
      employeeId,
      overallScore: basePerformance,
      tasksCompleted: 15 + Math.floor(Math.random() * 20),
      averageCompletionTime: 8 + Math.random() * 4,
      onTimeDelivery: 0.85 + Math.random() * 0.1,
      qualityRating: 4.2 + Math.random() * 0.6,
      skillAreas: [
        { skill: 'Technical Skills', score: basePerformance + 5 },
        { skill: 'Communication', score: basePerformance - 2 },
        { skill: 'Problem Solving', score: basePerformance + 3 },
        { skill: 'Time Management', score: basePerformance }
      ],
      recentTasks: [
        { title: 'Feature Implementation', score: basePerformance + 8, completedIn: 6 },
        { title: 'Bug Fixes', score: basePerformance + 5, completedIn: 4 },
        { title: 'Code Review', score: basePerformance + 2, completedIn: 2 }
      ]
    };
  }
}

export default GEKService;
