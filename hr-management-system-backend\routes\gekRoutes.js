const express = require('express');
const router = express.Router();
const { authenticate, authorizeRoles } = require('../middleware/authmiddleware');
const {
  calculateFitScore,
  getRankedEmployeesForTask,
  calculateTaskMetrics,
  calculateAttendanceReliability,
  getEvaluationMetrics
} = require('../controllers/gekController');
const GEKEstimate = require('../models/GEKEstimate');
const User = require('../models/User');

/**
 * GEK Routes - Endpoints for the General Estimating Knowledge system
 * Provides APIs for calculating fit scores, estimating task completion times,
 * and ranking employees for task assignments
 */

// ==================== FIT SCORE ENDPOINTS ====================

/**
 * @route GET /api/gek/fit-score/:userId
 * @desc Get fit score for a user and task category
 * @access Private (HR only)
 */
router.get('/fit-score/:userId', authenticate, authorizeRoles('hr'), async (req, res) => {
  try {
    const { userId } = req.params;
    const { category, priority = 'Medium', recalculate = false } = req.query;

    // Validate required parameters
    if (!category) {
      return res.status(400).json({ message: 'Task category is required' });
    }

    // Check if user exists
    const user = await User.findById(userId);
    if (!user) {
      return res.status(404).json({ message: 'User not found' });
    }

    // Try to get existing estimate first if recalculate is false
    if (recalculate === 'false' || recalculate === false) {
      const existingEstimate = await GEKEstimate.findOne({
        userId,
        taskCategory: category,
        taskPriority: priority
      });

      // If we have a recent estimate, return it
      if (existingEstimate && existingEstimate.isRecent) {
        return res.status(200).json({
          message: 'Fit score retrieved from cache',
          estimate: existingEstimate,
          cached: true
        });
      }
    }

    // Calculate new fit score
    const fitScoreData = await calculateFitScore(userId, category, priority);

    res.status(200).json({
      message: 'Fit score calculated successfully',
      estimate: fitScoreData,
      cached: false
    });
  } catch (error) {
    console.error('Error getting fit score:', error);
    res.status(500).json({
      message: 'Error calculating fit score',
      error: error.message
    });
  }
});

/**
 * @route GET /api/gek/user-estimates/:userId
 * @desc Get all fit score estimates for a user
 * @access Private (HR only)
 */
router.get('/user-estimates/:userId', authenticate, authorizeRoles('hr'), async (req, res) => {
  try {
    const { userId } = req.params;

    // Check if user exists
    const user = await User.findById(userId);
    if (!user) {
      return res.status(404).json({ message: 'User not found' });
    }

    // Get all estimates for the user
    const estimates = await GEKEstimate.find({ userId });

    res.status(200).json({
      message: 'User estimates retrieved successfully',
      estimates,
      user: {
        _id: user._id,
        name: user.name,
        email: user.email,
        job: user.job,
        department: user.department
      }
    });
  } catch (error) {
    console.error('Error getting user estimates:', error);
    res.status(500).json({
      message: 'Error retrieving user estimates',
      error: error.message
    });
  }
});

// ==================== TASK ASSIGNMENT ENDPOINTS ====================

/**
 * @route POST /api/gek/ranked-employees
 * @desc Get ranked employees for a task
 * @access Private (HR only)
 */
router.post('/ranked-employees', authenticate, authorizeRoles('hr'), async (req, res) => {
  try {
    const { taskDetails, userIds, limit = 5 } = req.body;

    // Validate required parameters
    if (!taskDetails || !taskDetails.category) {
      return res.status(400).json({ message: 'Task category is required' });
    }

    // Get ranked employees
    const rankedEmployees = await getRankedEmployeesForTask(taskDetails, userIds, limit);

    res.status(200).json({
      message: 'Ranked employees retrieved successfully',
      rankedEmployees
    });
  } catch (error) {
    console.error('Error getting ranked employees:', error);
    res.status(500).json({
      message: 'Error retrieving ranked employees',
      error: error.message
    });
  }
});

/**
 * @route GET /api/gek/department-ranking
 * @desc Get ranked employees within a department for a task
 * @access Private (HR only)
 */
router.get('/department-ranking', authenticate, authorizeRoles('hr'), async (req, res) => {
  try {
    const { department, category, priority = 'Medium', limit = 10 } = req.query;

    // Validate required parameters
    if (!department || !category) {
      return res.status(400).json({ message: 'Department and task category are required' });
    }

    // Get users in the department
    const departmentUsers = await User.find({
      department,
      role: 'user',
      active: true
    }).select('_id');

    const userIds = departmentUsers.map(user => user._id);

    // Get ranked employees
    const rankedEmployees = await getRankedEmployeesForTask(
      { category, priority },
      userIds,
      limit
    );

    res.status(200).json({
      message: 'Department ranking retrieved successfully',
      department,
      rankedEmployees
    });
  } catch (error) {
    console.error('Error getting department ranking:', error);
    res.status(500).json({
      message: 'Error retrieving department ranking',
      error: error.message
    });
  }
});

// ==================== METRICS ENDPOINTS ====================

/**
 * @route GET /api/gek/task-metrics/:userId
 * @desc Get task performance metrics for a user
 * @access Private (HR only)
 */
router.get('/task-metrics/:userId', authenticate, authorizeRoles('hr'), async (req, res) => {
  try {
    const { userId } = req.params;
    const { category, days = 90 } = req.query;

    // Check if user exists
    const user = await User.findById(userId);
    if (!user) {
      return res.status(404).json({ message: 'User not found' });
    }

    // Get task metrics
    const metrics = await calculateTaskMetrics(userId, category, parseInt(days));

    res.status(200).json({
      message: 'Task metrics retrieved successfully',
      metrics
    });
  } catch (error) {
    console.error('Error getting task metrics:', error);
    res.status(500).json({
      message: 'Error retrieving task metrics',
      error: error.message
    });
  }
});

/**
 * @route GET /api/gek/attendance-metrics/:userId
 * @desc Get attendance metrics for a user
 * @access Private (HR only)
 */
router.get('/attendance-metrics/:userId', authenticate, authorizeRoles('hr'), async (req, res) => {
  try {
    const { userId } = req.params;
    const { days = 90 } = req.query;

    // Check if user exists
    const user = await User.findById(userId);
    if (!user) {
      return res.status(404).json({ message: 'User not found' });
    }

    // Get attendance metrics
    const metrics = await calculateAttendanceReliability(userId, parseInt(days));

    res.status(200).json({
      message: 'Attendance metrics retrieved successfully',
      metrics
    });
  } catch (error) {
    console.error('Error getting attendance metrics:', error);
    res.status(500).json({
      message: 'Error retrieving attendance metrics',
      error: error.message
    });
  }
});

/**
 * @route GET /api/gek/evaluation-metrics/:userId
 * @desc Get evaluation metrics for a user
 * @access Private (HR only)
 */
router.get('/evaluation-metrics/:userId', authenticate, authorizeRoles('hr'), async (req, res) => {
  try {
    const { userId } = req.params;
    const { days = 90 } = req.query;

    // Check if user exists
    const user = await User.findById(userId);
    if (!user) {
      return res.status(404).json({ message: 'User not found' });
    }

    // Get evaluation metrics
    const metrics = await getEvaluationMetrics(userId, parseInt(days));

    res.status(200).json({
      message: 'Evaluation metrics retrieved successfully',
      metrics
    });
  } catch (error) {
    console.error('Error getting evaluation metrics:', error);
    res.status(500).json({
      message: 'Error retrieving evaluation metrics',
      error: error.message
    });
  }
});

module.exports = router;
