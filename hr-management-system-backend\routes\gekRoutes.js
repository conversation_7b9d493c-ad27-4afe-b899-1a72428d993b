const express = require('express');
const router = express.Router();
const { authenticate, authorizeRoles } = require('../middleware/authmiddleware');
const {
  calculateFitScore,
  getRankedEmployeesForTask,
  calculateTaskMetrics,
  calculateAttendanceReliability,
  getEvaluationMetrics
} = require('../controllers/gekController');
const GEKEstimate = require('../models/GEKEstimate');
const User = require('../models/User');

/**
 * GEK Routes - Endpoints for the General Estimating Knowledge system
 * Provides APIs for calculating fit scores, estimating task completion times,
 * and ranking employees for task assignments
 */

// ==================== FIT SCORE ENDPOINTS ====================

/**
 * @route GET /api/gek/fit-score/:userId
 * @desc Get fit score for a user and task category
 * @access Private (HR only)
 */
router.get('/fit-score/:userId', authenticate, authorizeRoles('hr'), async (req, res) => {
  try {
    const { userId } = req.params;
    const { category, priority = 'Medium', recalculate = false } = req.query;

    // Validate required parameters
    if (!category) {
      return res.status(400).json({ message: 'Task category is required' });
    }

    // Check if user exists
    const user = await User.findById(userId);
    if (!user) {
      return res.status(404).json({ message: 'User not found' });
    }

    // Try to get existing estimate first if recalculate is false
    if (recalculate === 'false' || recalculate === false) {
      const existingEstimate = await GEKEstimate.findOne({
        userId,
        taskCategory: category,
        taskPriority: priority
      });

      // If we have a recent estimate, return it
      if (existingEstimate && existingEstimate.isRecent) {
        return res.status(200).json({
          message: 'Fit score retrieved from cache',
          estimate: existingEstimate,
          cached: true
        });
      }
    }

    // Calculate new fit score
    const fitScoreData = await calculateFitScore(userId, category, priority);

    res.status(200).json({
      message: 'Fit score calculated successfully',
      estimate: fitScoreData,
      cached: false
    });
  } catch (error) {
    console.error('Error getting fit score:', error);
    res.status(500).json({
      message: 'Error calculating fit score',
      error: error.message
    });
  }
});

/**
 * @route GET /api/gek/user-estimates/:userId
 * @desc Get all fit score estimates for a user
 * @access Private (HR only)
 */
router.get('/user-estimates/:userId', authenticate, authorizeRoles('hr'), async (req, res) => {
  try {
    const { userId } = req.params;

    // Check if user exists
    const user = await User.findById(userId);
    if (!user) {
      return res.status(404).json({ message: 'User not found' });
    }

    // Get all estimates for the user
    const estimates = await GEKEstimate.find({ userId });

    res.status(200).json({
      message: 'User estimates retrieved successfully',
      estimates,
      user: {
        _id: user._id,
        name: user.name,
        email: user.email,
        job: user.job,
        department: user.department
      }
    });
  } catch (error) {
    console.error('Error getting user estimates:', error);
    res.status(500).json({
      message: 'Error retrieving user estimates',
      error: error.message
    });
  }
});

// ==================== TASK ASSIGNMENT ENDPOINTS ====================

/**
 * @route POST /api/gek/ranked-employees
 * @desc Get ranked employees for a task
 * @access Private (HR only)
 */
router.post('/ranked-employees', authenticate, authorizeRoles('hr'), async (req, res) => {
  try {
    const { taskDetails, userIds, limit = 5 } = req.body;

    // Validate required parameters
    if (!taskDetails || !taskDetails.category) {
      return res.status(400).json({ message: 'Task category is required' });
    }

    // Get ranked employees
    const rankedEmployees = await getRankedEmployeesForTask(taskDetails, userIds, limit);

    res.status(200).json({
      message: 'Ranked employees retrieved successfully',
      rankedEmployees
    });
  } catch (error) {
    console.error('Error getting ranked employees:', error);
    res.status(500).json({
      message: 'Error retrieving ranked employees',
      error: error.message
    });
  }
});

/**
 * @route GET /api/gek/department-ranking
 * @desc Get ranked employees within a department for a task
 * @access Private (HR only)
 */
router.get('/department-ranking', authenticate, authorizeRoles('hr'), async (req, res) => {
  try {
    const { department, category, priority = 'Medium', limit = 10 } = req.query;

    // Validate required parameters
    if (!department || !category) {
      return res.status(400).json({ message: 'Department and task category are required' });
    }

    // Get users in the department
    const departmentUsers = await User.find({
      department,
      role: 'user',
      active: true
    }).select('_id');

    const userIds = departmentUsers.map(user => user._id);

    // Get ranked employees
    const rankedEmployees = await getRankedEmployeesForTask(
      { category, priority },
      userIds,
      limit
    );

    res.status(200).json({
      message: 'Department ranking retrieved successfully',
      department,
      rankedEmployees
    });
  } catch (error) {
    console.error('Error getting department ranking:', error);
    res.status(500).json({
      message: 'Error retrieving department ranking',
      error: error.message
    });
  }
});

// ==================== METRICS ENDPOINTS ====================

/**
 * @route GET /api/gek/task-metrics/:userId
 * @desc Get task performance metrics for a user
 * @access Private (HR only)
 */
router.get('/task-metrics/:userId', authenticate, authorizeRoles('hr'), async (req, res) => {
  try {
    const { userId } = req.params;
    const { category, days = 90 } = req.query;

    // Check if user exists
    const user = await User.findById(userId);
    if (!user) {
      return res.status(404).json({ message: 'User not found' });
    }

    // Get task metrics
    const metrics = await calculateTaskMetrics(userId, category, parseInt(days));

    res.status(200).json({
      message: 'Task metrics retrieved successfully',
      metrics
    });
  } catch (error) {
    console.error('Error getting task metrics:', error);
    res.status(500).json({
      message: 'Error retrieving task metrics',
      error: error.message
    });
  }
});

/**
 * @route GET /api/gek/attendance-metrics/:userId
 * @desc Get attendance metrics for a user
 * @access Private (HR only)
 */
router.get('/attendance-metrics/:userId', authenticate, authorizeRoles('hr'), async (req, res) => {
  try {
    const { userId } = req.params;
    const { days = 90 } = req.query;

    // Check if user exists
    const user = await User.findById(userId);
    if (!user) {
      return res.status(404).json({ message: 'User not found' });
    }

    // Get attendance metrics
    const metrics = await calculateAttendanceReliability(userId, parseInt(days));

    res.status(200).json({
      message: 'Attendance metrics retrieved successfully',
      metrics
    });
  } catch (error) {
    console.error('Error getting attendance metrics:', error);
    res.status(500).json({
      message: 'Error retrieving attendance metrics',
      error: error.message
    });
  }
});

/**
 * @route GET /api/gek/evaluation-metrics/:userId
 * @desc Get evaluation metrics for a user
 * @access Private (HR only)
 */
router.get('/evaluation-metrics/:userId', authenticate, authorizeRoles('hr'), async (req, res) => {
  try {
    const { userId } = req.params;
    const { days = 90 } = req.query;

    // Check if user exists
    const user = await User.findById(userId);
    if (!user) {
      return res.status(404).json({ message: 'User not found' });
    }

    // Get evaluation metrics
    const metrics = await getEvaluationMetrics(userId, parseInt(days));

    res.status(200).json({
      message: 'Evaluation metrics retrieved successfully',
      metrics
    });
  } catch (error) {
    console.error('Error getting evaluation metrics:', error);
    res.status(500).json({
      message: 'Error retrieving evaluation metrics',
      error: error.message
    });
  }
});

// ==================== TASK INTEGRATION ENDPOINTS ====================

/**
 * @route GET /api/gek/available-tasks
 * @desc Get available tasks for GEK assignment
 * @access Private (HR only)
 */
router.get('/available-tasks', authenticate, authorizeRoles('hr'), async (req, res) => {
  try {
    const { status = 'Assigned', category, priority, search, limit = 20 } = req.query;
    const Task = require('../models/Task');

    // Build query for available tasks
    const query = { createdBy: req.user.id };

    if (status) query.status = status;
    if (category) query.category = category;
    if (priority) query.priority = priority;

    // Text search
    if (search) {
      query.$or = [
        { title: { $regex: search, $options: 'i' } },
        { description: { $regex: search, $options: 'i' } }
      ];
    }

    const tasks = await Task.find(query)
      .populate('assignedTo', 'name email job department')
      .populate('createdBy', 'name email')
      .sort({ deadline: 1 })
      .limit(parseInt(limit));

    res.status(200).json({
      message: 'Available tasks retrieved successfully',
      tasks,
      total: tasks.length
    });
  } catch (error) {
    console.error('Error getting available tasks:', error);
    res.status(500).json({ message: 'Server error' });
  }
});

/**
 * @route GET /api/gek/task-employee-match/:taskId
 * @desc Get best employee matches for a specific task
 * @access Private (HR only)
 */
router.get('/task-employee-match/:taskId', authenticate, authorizeRoles('hr'), async (req, res) => {
  try {
    const { taskId } = req.params;
    const { limit = 10 } = req.query;
    const Task = require('../models/Task');

    // Get task details
    const task = await Task.findById(taskId);
    if (!task) {
      return res.status(404).json({ message: 'Task not found' });
    }

    // Get ranked employees for this task
    const rankedEmployees = await getRankedEmployeesForTask(
      task.category,
      task.priority,
      null, // No specific user IDs
      parseInt(limit)
    );

    res.status(200).json({
      message: 'Employee matches retrieved successfully',
      task: {
        _id: task._id,
        title: task.title,
        category: task.category,
        priority: task.priority,
        deadline: task.deadline
      },
      rankedEmployees: rankedEmployees.rankedEmployees || []
    });
  } catch (error) {
    console.error('Error getting task-employee matches:', error);
    res.status(500).json({ message: 'Server error' });
  }
});

// ==================== ANALYTICS ENDPOINTS ====================

/**
 * @route GET /api/gek/analytics/overview
 * @desc Get GEK system overview analytics
 * @access Private (HR only)
 */
router.get('/analytics/overview', authenticate, authorizeRoles('hr'), async (req, res) => {
  try {
    const { days = 90 } = req.query;
    const timePeriod = parseInt(days);
    const startDate = new Date(Date.now() - timePeriod * 24 * 60 * 60 * 1000);

    // Get total estimates
    const totalEstimates = await GEKEstimate.countDocuments({
      lastUpdated: { $gte: startDate }
    });

    // Get average fit scores by category
    const avgFitScoresByCategory = await GEKEstimate.aggregate([
      { $match: { lastUpdated: { $gte: startDate } } },
      {
        $group: {
          _id: '$taskCategory',
          avgFitScore: { $avg: '$fitScore' },
          count: { $sum: 1 }
        }
      },
      { $sort: { avgFitScore: -1 } }
    ]);

    // Get top performers
    const topPerformers = await GEKEstimate.aggregate([
      { $match: { lastUpdated: { $gte: startDate } } },
      {
        $group: {
          _id: '$userId',
          avgFitScore: { $avg: '$fitScore' },
          totalEstimates: { $sum: 1 }
        }
      },
      { $match: { totalEstimates: { $gte: 3 } } }, // At least 3 estimates
      { $sort: { avgFitScore: -1 } },
      { $limit: 10 }
    ]);

    // Populate user details for top performers
    await User.populate(topPerformers, { path: '_id', select: 'name job department' });

    res.status(200).json({
      message: 'GEK analytics overview retrieved successfully',
      analytics: {
        totalEstimates,
        avgFitScoresByCategory,
        topPerformers: topPerformers.map(performer => ({
          user: performer._id,
          avgFitScore: Math.round(performer.avgFitScore),
          totalEstimates: performer.totalEstimates
        })),
        timePeriod
      }
    });
  } catch (error) {
    console.error('Error getting GEK analytics overview:', error);
    res.status(500).json({ message: 'Server error' });
  }
});

/**
 * @route GET /api/gek/analytics/department-performance
 * @desc Get department-wise GEK performance analytics
 * @access Private (HR only)
 */
router.get('/analytics/department-performance', authenticate, authorizeRoles('hr'), async (req, res) => {
  try {
    const { days = 90 } = req.query;
    const timePeriod = parseInt(days);
    const startDate = new Date(Date.now() - timePeriod * 24 * 60 * 60 * 1000);

    // Get department performance data
    const departmentPerformance = await GEKEstimate.aggregate([
      { $match: { lastUpdated: { $gte: startDate } } },
      {
        $lookup: {
          from: 'users',
          localField: 'userId',
          foreignField: '_id',
          as: 'user'
        }
      },
      { $unwind: '$user' },
      {
        $group: {
          _id: '$user.department',
          avgFitScore: { $avg: '$fitScore' },
          avgCompletionTime: { $avg: '$estimatedCompletionTime' },
          totalEstimates: { $sum: 1 },
          avgConfidenceScore: { $avg: '$confidenceScore' },
          employeeCount: { $addToSet: '$userId' }
        }
      },
      {
        $project: {
          department: '$_id',
          avgFitScore: { $round: ['$avgFitScore', 1] },
          avgCompletionTime: { $round: ['$avgCompletionTime', 1] },
          totalEstimates: 1,
          avgConfidenceScore: { $round: ['$avgConfidenceScore', 1] },
          employeeCount: { $size: '$employeeCount' }
        }
      },
      { $sort: { avgFitScore: -1 } }
    ]);

    res.status(200).json({
      message: 'Department performance analytics retrieved successfully',
      analytics: {
        departmentPerformance,
        timePeriod
      }
    });
  } catch (error) {
    console.error('Error getting department performance analytics:', error);
    res.status(500).json({ message: 'Server error' });
  }
});

/**
 * @route GET /api/gek/analytics/trends
 * @desc Get GEK performance trends over time
 * @access Private (HR only)
 */
router.get('/analytics/trends', authenticate, authorizeRoles('hr'), async (req, res) => {
  try {
    const { days = 90, interval = 'week' } = req.query;
    const timePeriod = parseInt(days);
    const startDate = new Date(Date.now() - timePeriod * 24 * 60 * 60 * 1000);

    // Determine grouping based on interval
    let dateGrouping;
    if (interval === 'day') {
      dateGrouping = {
        year: { $year: '$lastUpdated' },
        month: { $month: '$lastUpdated' },
        day: { $dayOfMonth: '$lastUpdated' }
      };
    } else if (interval === 'week') {
      dateGrouping = {
        year: { $year: '$lastUpdated' },
        week: { $week: '$lastUpdated' }
      };
    } else {
      dateGrouping = {
        year: { $year: '$lastUpdated' },
        month: { $month: '$lastUpdated' }
      };
    }

    // Get trends data
    const trends = await GEKEstimate.aggregate([
      { $match: { lastUpdated: { $gte: startDate } } },
      {
        $group: {
          _id: dateGrouping,
          avgFitScore: { $avg: '$fitScore' },
          avgCompletionTime: { $avg: '$estimatedCompletionTime' },
          totalEstimates: { $sum: 1 },
          avgConfidenceScore: { $avg: '$confidenceScore' }
        }
      },
      { $sort: { '_id.year': 1, '_id.month': 1, '_id.week': 1, '_id.day': 1 } }
    ]);

    res.status(200).json({
      message: 'GEK trends analytics retrieved successfully',
      analytics: {
        trends: trends.map(trend => ({
          period: trend._id,
          avgFitScore: Math.round(trend.avgFitScore * 10) / 10,
          avgCompletionTime: Math.round(trend.avgCompletionTime * 10) / 10,
          totalEstimates: trend.totalEstimates,
          avgConfidenceScore: Math.round(trend.avgConfidenceScore * 10) / 10
        })),
        interval,
        timePeriod
      }
    });
  } catch (error) {
    console.error('Error getting trends analytics:', error);
    res.status(500).json({ message: 'Server error' });
  }
});

/**
 * @route PUT /api/gek/reassign-task/:taskId
 * @desc Reassign a task to a different employee using GEK recommendations
 * @access Private (HR only)
 */
router.put('/reassign-task/:taskId', authenticate, authorizeRoles('hr'), async (req, res) => {
  try {
    const { taskId } = req.params;
    const { newAssigneeId, reason } = req.body;
    const Task = require('../models/Task');

    if (!newAssigneeId) {
      return res.status(400).json({ message: 'New assignee ID is required' });
    }

    // Get task and validate
    const task = await Task.findById(taskId);
    if (!task) {
      return res.status(404).json({ message: 'Task not found' });
    }

    if (task.createdBy.toString() !== req.user.id) {
      return res.status(403).json({ message: 'You can only reassign your own tasks' });
    }

    // Validate new assignee exists
    const newAssignee = await User.findById(newAssigneeId);
    if (!newAssignee) {
      return res.status(400).json({ message: 'New assignee not found' });
    }

    // Get fit score for new assignee
    const fitScoreData = await calculateFitScore(newAssigneeId, task.category, task.priority);

    // Update task
    const oldAssigneeId = task.assignedTo;
    task.assignedTo = newAssigneeId;
    task.updates.push({
      userId: req.user.id,
      message: `Task reassigned from ${oldAssigneeId} to ${newAssigneeId}. Reason: ${reason || 'GEK recommendation'}`,
      timestamp: new Date()
    });

    await task.save();

    // Populate task details
    const updatedTask = await Task.findById(taskId)
      .populate('assignedTo', 'name email job department')
      .populate('createdBy', 'name email');

    res.status(200).json({
      message: 'Task reassigned successfully',
      task: updatedTask,
      fitScore: fitScoreData.fitScore,
      estimatedCompletionTime: fitScoreData.estimatedCompletionTime
    });
  } catch (error) {
    console.error('Error reassigning task:', error);
    res.status(500).json({ message: 'Server error' });
  }
});

module.exports = router;
