import React, { useState, useEffect } from 'react';
import {
  Box,
  Typography,
  Grid,
  TextField,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Card,
  CardContent,
  Avatar,
  List,
  ListItem,
  ListItemText,
  ListItemAvatar,
  Chip,
  Button,
  CircularProgress,
  Alert,
  Divider,
  Paper,
  Tooltip,
  IconButton,
  Collapse
} from '@mui/material';
import {
  Person,
  TrendingUp,
  Speed,
  Schedule,
  ExpandMore,
  ExpandLess,
  Assignment,
  CheckCircle,
  Info,
  Refresh
} from '@mui/icons-material';
import { showSuccessToast, showErrorToast, TOAST_CATEGORIES } from '../../Utils/toastUtils';
import GEKService from '../../Services/GEKService';

/**
 * GEK Task Creation Component
 * Enhanced task creation form that automatically shows GEK-based employee recommendations
 */
const GEKTaskCreation = ({ 
  taskData, 
  setTaskData, 
  onTaskCreate, 
  users = [],
  loading = false 
}) => {
  // State for GEK recommendations
  const [gekRecommendations, setGekRecommendations] = useState([]);
  const [gekLoading, setGekLoading] = useState(false);
  const [gekError, setGekError] = useState(null);
  const [showRecommendations, setShowRecommendations] = useState(false);
  const [selectedEmployee, setSelectedEmployee] = useState(null);

  // Task categories and priorities
  const taskCategories = [
    'General', 'Development', 'Design', 'Marketing', 'HR',
    'Finance', 'Operations', 'Project', 'Administrative',
    'Training', 'Evaluation', 'Other'
  ];

  const taskPriorities = ['Low', 'Medium', 'High', 'Urgent'];

  // Auto-fetch GEK recommendations when task details change
  useEffect(() => {
    if (taskData.category && taskData.priority && taskData.title) {
      fetchGEKRecommendations();
    }
  }, [taskData.category, taskData.priority]);

  // Fetch GEK recommendations
  const fetchGEKRecommendations = async () => {
    if (!taskData.category || !taskData.priority) return;

    setGekLoading(true);
    setGekError(null);

    try {
      const response = await GEKService.getRecommendedEmployees({
        category: taskData.category,
        priority: taskData.priority,
        estimatedHours: 0,
        limit: 8
      });

      if (response && response.recommendedEmployees) {
        setGekRecommendations(response.recommendedEmployees);
        setShowRecommendations(true);
        
        // Auto-select the best match if no employee is currently selected
        if (!taskData.assignedTo && response.recommendedEmployees.length > 0) {
          const bestMatch = response.recommendedEmployees[0];
          setSelectedEmployee(bestMatch);
          setTaskData(prev => ({
            ...prev,
            assignedTo: bestMatch.user._id
          }));
        }
      } else {
        setGekRecommendations([]);
      }
    } catch (err) {
      console.error('Error fetching GEK recommendations:', err);
      setGekError('Failed to get employee recommendations');
      setGekRecommendations([]);
    } finally {
      setGekLoading(false);
    }
  };

  // Handle employee selection
  const handleEmployeeSelect = (employee) => {
    setSelectedEmployee(employee);
    setTaskData(prev => ({
      ...prev,
      assignedTo: employee.user._id
    }));
  };

  // Handle manual assignment (fallback to regular dropdown)
  const handleManualAssignment = (userId) => {
    const user = users.find(u => u._id === userId);
    if (user) {
      setSelectedEmployee({ user, fitScore: 0, estimatedCompletionTime: 0 });
      setTaskData(prev => ({
        ...prev,
        assignedTo: userId
      }));
    }
  };

  // Get priority color
  const getPriorityColor = (priority) => {
    switch (priority) {
      case 'Urgent': return 'error';
      case 'High': return 'warning';
      case 'Medium': return 'info';
      case 'Low': return 'success';
      default: return 'default';
    }
  };

  // Get fit score color
  const getFitScoreColor = (score) => {
    if (score >= 80) return 'success';
    if (score >= 60) return 'warning';
    return 'error';
  };

  return (
    <Box>
      {/* Task Details Form */}
      <Grid container spacing={2}>
        <Grid item xs={12}>
          <TextField
            label="Task Title"
            fullWidth
            required
            value={taskData.title}
            onChange={(e) => setTaskData({ ...taskData, title: e.target.value })}
            placeholder="Enter a clear and descriptive task title"
          />
        </Grid>

        <Grid item xs={12}>
          <TextField
            label="Description"
            fullWidth
            multiline
            rows={3}
            value={taskData.description}
            onChange={(e) => setTaskData({ ...taskData, description: e.target.value })}
            placeholder="Provide detailed task requirements and instructions"
          />
        </Grid>

        <Grid item xs={12} sm={4}>
          <FormControl fullWidth required>
            <InputLabel>Category</InputLabel>
            <Select
              value={taskData.category}
              label="Category"
              onChange={(e) => setTaskData({ ...taskData, category: e.target.value })}
            >
              {taskCategories.map((category) => (
                <MenuItem key={category} value={category}>{category}</MenuItem>
              ))}
            </Select>
          </FormControl>
        </Grid>

        <Grid item xs={12} sm={4}>
          <FormControl fullWidth required>
            <InputLabel>Priority</InputLabel>
            <Select
              value={taskData.priority}
              label="Priority"
              onChange={(e) => setTaskData({ ...taskData, priority: e.target.value })}
            >
              {taskPriorities.map((priority) => (
                <MenuItem key={priority} value={priority}>{priority}</MenuItem>
              ))}
            </Select>
          </FormControl>
        </Grid>

        <Grid item xs={12} sm={4}>
          <TextField
            label="Deadline"
            type="date"
            fullWidth
            required
            value={taskData.deadline}
            onChange={(e) => setTaskData({ ...taskData, deadline: e.target.value })}
            InputLabelProps={{ shrink: true }}
            inputProps={{ min: new Date().toISOString().split('T')[0] }}
          />
        </Grid>
      </Grid>

      {/* GEK Recommendations Section */}
      {(taskData.category && taskData.priority) && (
        <Paper sx={{ mt: 3, p: 3 }}>
          <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between', mb: 2 }}>
            <Box sx={{ display: 'flex', alignItems: 'center' }}>
              <TrendingUp sx={{ mr: 1, color: 'primary.main' }} />
              <Typography variant="h6" fontWeight="medium">
                GEK Employee Recommendations
              </Typography>
              {gekLoading && <CircularProgress size={20} sx={{ ml: 2 }} />}
            </Box>
            <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
              <Tooltip title="Refresh recommendations">
                <IconButton 
                  size="small" 
                  onClick={fetchGEKRecommendations}
                  disabled={gekLoading}
                >
                  <Refresh />
                </IconButton>
              </Tooltip>
              <IconButton
                size="small"
                onClick={() => setShowRecommendations(!showRecommendations)}
              >
                {showRecommendations ? <ExpandLess /> : <ExpandMore />}
              </IconButton>
            </Box>
          </Box>

          <Collapse in={showRecommendations}>
            {gekError ? (
              <Alert 
                severity="warning" 
                sx={{ mb: 2 }}
                action={
                  <Button size="small" onClick={fetchGEKRecommendations}>
                    Retry
                  </Button>
                }
              >
                {gekError}
              </Alert>
            ) : gekRecommendations.length > 0 ? (
              <>
                <Alert severity="info" sx={{ mb: 2 }}>
                  <Typography variant="body2">
                    Based on performance history, skills, and availability, here are the best-fit employees for this{' '}
                    <Chip label={taskData.category} size="small" sx={{ mx: 0.5 }} />{' '}
                    task with{' '}
                    <Chip label={taskData.priority} size="small" color={getPriorityColor(taskData.priority)} sx={{ mx: 0.5 }} />{' '}
                    priority.
                  </Typography>
                </Alert>

                <List>
                  {gekRecommendations.slice(0, 5).map((recommendation, index) => (
                    <ListItem
                      key={recommendation.user._id}
                      button
                      onClick={() => handleEmployeeSelect(recommendation)}
                      selected={selectedEmployee?.user._id === recommendation.user._id}
                      sx={{
                        border: '1px solid',
                        borderColor: selectedEmployee?.user._id === recommendation.user._id ? 'primary.main' : 'divider',
                        borderRadius: 1,
                        mb: 1,
                        bgcolor: index === 0 ? 'action.hover' : 'background.paper'
                      }}
                    >
                      <ListItemAvatar>
                        <Avatar sx={{ 
                          bgcolor: index === 0 ? 'secondary.main' : 'primary.main',
                          fontWeight: 'bold'
                        }}>
                          {index + 1}
                        </Avatar>
                      </ListItemAvatar>
                      <ListItemText
                        primary={
                          <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                            <Typography variant="subtitle1" fontWeight="medium">
                              {recommendation.user.name}
                            </Typography>
                            {index === 0 && (
                              <Chip label="Best Match" size="small" color="secondary" />
                            )}
                          </Box>
                        }
                        secondary={
                          <Box>
                            <Typography variant="body2" color="text.secondary">
                              {recommendation.user.job} • {recommendation.user.department}
                            </Typography>
                            <Box sx={{ display: 'flex', gap: 2, mt: 0.5 }}>
                              <Chip
                                label={`${Math.round(recommendation.fitScore)}% Fit`}
                                size="small"
                                color={getFitScoreColor(recommendation.fitScore)}
                                icon={<Speed />}
                              />
                              <Chip
                                label={`~${Math.round(recommendation.estimatedCompletionTime)}h`}
                                size="small"
                                variant="outlined"
                                icon={<Schedule />}
                              />
                            </Box>
                          </Box>
                        }
                      />
                      {selectedEmployee?.user._id === recommendation.user._id && (
                        <CheckCircle color="primary" />
                      )}
                    </ListItem>
                  ))}
                </List>
              </>
            ) : (
              <Alert severity="info">
                <Typography variant="body2">
                  No GEK recommendations available. You can manually assign the task using the dropdown below.
                </Typography>
              </Alert>
            )}
          </Collapse>
        </Paper>
      )}

      {/* Manual Assignment Fallback */}
      {(!gekRecommendations.length || gekError) && (
        <Box sx={{ mt: 3 }}>
          <FormControl fullWidth required>
            <InputLabel>Assign To (Manual Selection)</InputLabel>
            <Select
              value={taskData.assignedTo}
              label="Assign To (Manual Selection)"
              onChange={(e) => handleManualAssignment(e.target.value)}
            >
              {users.map((user) => (
                <MenuItem key={user._id} value={user._id}>
                  <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                    <Person fontSize="small" />
                    {user.name} ({user.job})
                  </Box>
                </MenuItem>
              ))}
            </Select>
          </FormControl>
        </Box>
      )}

      {/* Selected Employee Summary */}
      {selectedEmployee && (
        <Card sx={{ mt: 3, bgcolor: 'primary.light', color: 'primary.contrastText' }}>
          <CardContent>
            <Typography variant="h6" gutterBottom>
              <Assignment sx={{ mr: 1, verticalAlign: 'middle' }} />
              Task Assignment Summary
            </Typography>
            <Grid container spacing={2}>
              <Grid item xs={12} sm={6}>
                <Typography variant="body2">
                  <strong>Assigned to:</strong> {selectedEmployee.user.name}
                </Typography>
                <Typography variant="body2">
                  <strong>Position:</strong> {selectedEmployee.user.job}
                </Typography>
                <Typography variant="body2">
                  <strong>Department:</strong> {selectedEmployee.user.department}
                </Typography>
              </Grid>
              <Grid item xs={12} sm={6}>
                {selectedEmployee.fitScore > 0 && (
                  <>
                    <Typography variant="body2">
                      <strong>Fit Score:</strong> {Math.round(selectedEmployee.fitScore)}%
                    </Typography>
                    <Typography variant="body2">
                      <strong>Estimated Time:</strong> {Math.round(selectedEmployee.estimatedCompletionTime)} hours
                    </Typography>
                  </>
                )}
              </Grid>
            </Grid>
          </CardContent>
        </Card>
      )}
    </Box>
  );
};

export default GEKTaskCreation;
