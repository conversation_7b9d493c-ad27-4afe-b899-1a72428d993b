import React, { useState, useEffect } from 'react';
import {
  Box,
  Typography,
  Paper,
  Grid,
  Card,
  CardContent,
  Divider,
  CircularProgress,
  Chip,
  Avatar,
  List,
  ListItem,
  ListItemText,
  ListItemAvatar,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Button,
  LinearProgress,
  Alert,
  Tabs,
  Tab
} from '@mui/material';
import {
  Person,
  Timeline,
  Speed,
  CheckCircle,
  Group,
  Category,
  PriorityHigh,
  Search,
  Error as ErrorIcon,
  Refresh as RefreshIcon,
  Assignment,
  Analytics,
  Dashboard
} from '@mui/icons-material';
import { showSuccessToast, showErrorToast, TOAST_CATEGORIES } from '../../Utils/toastUtils';
import GEKService from '../../Services/GEKService';
import GEKTaskList from './GEKTaskList';
import GEKAnalytics from './GEKAnalytics';

/**
 * GEK Dashboard Component
 * Displays the General Estimating Knowledge dashboard for HR users
 */
const GEKDashboard = () => {
  // State for tab navigation
  const [activeTab, setActiveTab] = useState(0);

  // State for filters
  const [selectedDepartment, setSelectedDepartment] = useState('');
  const [selectedCategory, setSelectedCategory] = useState('General');
  const [selectedPriority, setSelectedPriority] = useState('Medium');
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);

  // Real data for departments from the user model
  const departments = [
    'Engineering', 'Marketing', 'Sales', 'HR',
    'Finance', 'Operations', 'IT', 'Customer Support',
    'Executive', 'Other'
  ];

  // Real data for task categories from the GEK model
  const taskCategories = [
    'General', 'Development', 'Design', 'Marketing', 'HR',
    'Finance', 'Operations', 'Project', 'Administrative',
    'Training', 'Evaluation', 'Other'
  ];

  // Real data for task priorities from the GEK model
  const taskPriorities = ['Low', 'Medium', 'High', 'Urgent'];

  // State for ranked employees
  const [rankedEmployees, setRankedEmployees] = useState([]);

  // State for selected user
  const [selectedUser, setSelectedUser] = useState(null);
  const [userEstimates, setUserEstimates] = useState([]);

  // Tab configuration
  const tabs = [
    { label: 'Overview', icon: <Dashboard />, value: 0 },
    { label: 'Task Management', icon: <Assignment />, value: 1 },
    { label: 'Analytics', icon: <Analytics />, value: 2 }
  ];

  // Handle tab change
  const handleTabChange = (event, newValue) => {
    setActiveTab(newValue);
  };

  // Load real data on component mount
  useEffect(() => {
    loadRankedEmployees();
  }, []);

  // Function to load ranked employees from the API
  const loadRankedEmployees = async () => {
    setLoading(true);
    setError(null);

    try {
      // Get ranked employees for the selected task category and priority
      const response = await GEKService.getRankedEmployees(
        {
          category: selectedCategory,
          priority: selectedPriority
        },
        null, // No specific user IDs
        10    // Limit to 10 results
      );

      if (response && response.rankedEmployees) {
        setRankedEmployees(response.rankedEmployees);
      } else {
        // If the response doesn't have the expected format, show an error
        setError('Invalid response format from the server');
        setRankedEmployees([]);
      }
    } catch (err) {
      console.error('Error loading ranked employees:', err);
      setError(err.friendlyMessage || 'Failed to load employee rankings');
      setRankedEmployees([]);
    } finally {
      setLoading(false);
    }
  };

  // Handle filter changes
  const handleFilterChange = async () => {
    setLoading(true);
    setError(null);

    try {
      let response;

      if (selectedDepartment) {
        // If a department is selected, get department ranking
        response = await GEKService.getDepartmentRanking(
          selectedDepartment,
          selectedCategory,
          selectedPriority,
          10 // Limit to 10 results
        );

        if (response && response.rankedEmployees) {
          setRankedEmployees(response.rankedEmployees);
        } else {
          setError('Invalid response format from the server');
          setRankedEmployees([]);
        }
      } else {
        // If no department is selected, get all ranked employees
        response = await GEKService.getRankedEmployees(
          {
            category: selectedCategory,
            priority: selectedPriority
          },
          null, // No specific user IDs
          10    // Limit to 10 results
        );

        if (response && response.rankedEmployees) {
          setRankedEmployees(response.rankedEmployees);
        } else {
          setError('Invalid response format from the server');
          setRankedEmployees([]);
        }
      }

      showSuccessToast('Employee rankings updated', TOAST_CATEGORIES.DATA, 'updated');
    } catch (err) {
      console.error('Error updating rankings:', err);
      setError(err.friendlyMessage || 'Failed to update employee rankings');
      setRankedEmployees([]);
    } finally {
      setLoading(false);
    }
  };

  // Handle user selection
  const handleUserSelect = async (user) => {
    setSelectedUser(user);
    setLoading(true);
    setError(null);

    try {
      // Get all estimates for the selected user
      const response = await GEKService.getUserEstimates(user._id);

      if (response && response.estimates) {
        setUserEstimates(response.estimates);
      } else {
        // If no estimates are found, try to get a fit score for the current category
        try {
          const fitScoreResponse = await GEKService.getFitScore(
            user._id,
            selectedCategory,
            selectedPriority,
            true // Force recalculation
          );

          if (fitScoreResponse && fitScoreResponse.estimate) {
            setUserEstimates([fitScoreResponse.estimate]);
          } else {
            setUserEstimates([]);
          }
        } catch (fitScoreErr) {
          console.error('Error getting fit score:', fitScoreErr);
          setUserEstimates([]);
        }
      }
    } catch (err) {
      console.error('Error loading user estimates:', err);
      setError(err.friendlyMessage || 'Failed to load user performance data');
      setUserEstimates([]);
    } finally {
      setLoading(false);
    }
  };

  // Render tab content
  const renderTabContent = () => {
    switch (activeTab) {
      case 0:
        return renderOverviewTab();
      case 1:
        return <GEKTaskList />;
      case 2:
        return <GEKAnalytics />;
      default:
        return renderOverviewTab();
    }
  };

  // Render overview tab (existing content)
  const renderOverviewTab = () => (
    <>
      {/* Error message */}
      {error && (
        <Alert
          severity="error"
          sx={{ mb: 3 }}
          action={
            <Button
              color="inherit"
              size="small"
              onClick={loadRankedEmployees}
            >
              Retry
            </Button>
          }
        >
          <Typography variant="body2">{error}</Typography>
        </Alert>
      )}

      {/* Filters */}
      <Paper sx={{ p: 3, mb: 4 }}>
        <Typography variant="h6" gutterBottom>
          <Category sx={{ mr: 1, verticalAlign: 'middle' }} />
          Task Assignment Filters
        </Typography>

        <Grid container spacing={2} alignItems="center">
          <Grid item xs={12} sm={3}>
            <FormControl fullWidth size="small">
              <InputLabel>Department</InputLabel>
              <Select
                value={selectedDepartment}
                onChange={(e) => setSelectedDepartment(e.target.value)}
                label="Department"
              >
                <MenuItem value="">All Departments</MenuItem>
                {departments.map((dept) => (
                  <MenuItem key={dept} value={dept}>{dept}</MenuItem>
                ))}
              </Select>
            </FormControl>
          </Grid>

          <Grid item xs={12} sm={3}>
            <FormControl fullWidth size="small">
              <InputLabel>Task Category</InputLabel>
              <Select
                value={selectedCategory}
                onChange={(e) => setSelectedCategory(e.target.value)}
                label="Task Category"
              >
                {taskCategories.map((category) => (
                  <MenuItem key={category} value={category}>{category}</MenuItem>
                ))}
              </Select>
            </FormControl>
          </Grid>

          <Grid item xs={12} sm={3}>
            <FormControl fullWidth size="small">
              <InputLabel>Priority</InputLabel>
              <Select
                value={selectedPriority}
                onChange={(e) => setSelectedPriority(e.target.value)}
                label="Priority"
              >
                {taskPriorities.map((priority) => (
                  <MenuItem key={priority} value={priority}>{priority}</MenuItem>
                ))}
              </Select>
            </FormControl>
          </Grid>

          <Grid item xs={12} sm={3}>
            <Button
              variant="contained"
              color="primary"
              onClick={handleFilterChange}
              startIcon={loading ? <CircularProgress size={20} color="inherit" /> : <Search />}
              fullWidth
              disabled={loading}
            >
              {loading ? 'Loading...' : 'Find Best Matches'}
            </Button>
          </Grid>
        </Grid>
      </Paper>

      {/* Ranked Employees */}
      <Grid container spacing={4}>
        <Grid item xs={12} md={7}>
          <Paper sx={{ p: 3, height: '100%' }}>
            <Typography variant="h6" gutterBottom>
              <Group sx={{ mr: 1, verticalAlign: 'middle' }} />
              Best-Fit Employees for {selectedCategory} Tasks
              {selectedDepartment && ` (${selectedDepartment} Department)`}
            </Typography>

            {loading ? (
              <Box display="flex" justifyContent="center" alignItems="center" minHeight="200px">
                <CircularProgress />
              </Box>
            ) : rankedEmployees.length === 0 ? (
              <Box sx={{ p: 3, textAlign: 'center' }}>
                <Typography color="text.secondary">
                  No employee data available for the selected criteria.
                </Typography>
                <Button
                  variant="outlined"
                  sx={{ mt: 2 }}
                  onClick={loadRankedEmployees}
                  startIcon={<RefreshIcon />}
                >
                  Refresh Data
                </Button>
              </Box>
            ) : (
              <List>
                {rankedEmployees.map((item, index) => (
                  <React.Fragment key={item.user._id}>
                    <ListItem
                      button
                      onClick={() => handleUserSelect(item.user)}
                      selected={selectedUser && selectedUser._id === item.user._id}
                      sx={{ borderRadius: 1, mb: 1 }}
                    >
                      <ListItemAvatar>
                        <Avatar sx={{ bgcolor: index < 3 ? 'secondary.main' : 'primary.main' }}>
                          {index + 1}
                        </Avatar>
                      </ListItemAvatar>
                      <ListItemText
                        primary={item.user.name}
                        secondary={`${item.user.job} • ${item.user.department}`}
                      />
                      <Box sx={{ display: 'flex', alignItems: 'center', flexDirection: 'column', minWidth: 100 }}>
                        <Typography variant="h6" color="primary" fontWeight="bold">
                          {Math.round(item.fitScore)}%
                        </Typography>
                        <Typography variant="caption" color="text.secondary">
                          Fit Score
                        </Typography>
                      </Box>
                      <Box sx={{ display: 'flex', alignItems: 'center', flexDirection: 'column', minWidth: 120 }}>
                        <Typography variant="body2">
                          ~{Math.round(item.estimatedCompletionTime)} hours
                        </Typography>
                        <Typography variant="caption" color="text.secondary">
                          Est. Completion
                        </Typography>
                      </Box>
                    </ListItem>
                    {index < rankedEmployees.length - 1 && <Divider variant="inset" component="li" />}
                  </React.Fragment>
                ))}
              </List>
            )}
          </Paper>
        </Grid>

        {/* User Details */}
        <Grid item xs={12} md={5}>
          <Paper sx={{ p: 3, height: '100%' }}>
            {selectedUser ? (
              <>
                <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 2 }}>
                  <Typography variant="h6">
                    <Person sx={{ mr: 1, verticalAlign: 'middle' }} />
                    Employee Performance Profile
                  </Typography>
                  {loading && (
                    <CircularProgress size={20} />
                  )}
                </Box>

                <Box sx={{ display: 'flex', alignItems: 'center', mb: 3 }}>
                  <Avatar sx={{ width: 56, height: 56, mr: 2, bgcolor: 'primary.main' }}>
                    {selectedUser.name.charAt(0)}
                  </Avatar>
                  <Box>
                    <Typography variant="h6">{selectedUser.name}</Typography>
                    <Typography variant="body2" color="text.secondary">
                      {selectedUser.job} • {selectedUser.department}
                    </Typography>
                  </Box>
                </Box>

                {loading ? (
                  <Box display="flex" justifyContent="center" alignItems="center" minHeight="200px">
                    <CircularProgress />
                  </Box>
                ) : userEstimates.length === 0 ? (
                  <Box sx={{ p: 3, textAlign: 'center' }}>
                    <Typography color="text.secondary">
                      No performance estimates available for this employee.
                    </Typography>
                    <Button
                      variant="outlined"
                      sx={{ mt: 2 }}
                      onClick={() => handleUserSelect(selectedUser)}
                      startIcon={<RefreshIcon />}
                    >
                      Calculate Estimates
                    </Button>
                  </Box>
                ) : (
                  <>
                    <Typography variant="subtitle1" gutterBottom>
                      Task Category Performance
                    </Typography>

                    <Grid container spacing={2} sx={{ mb: 3 }}>
                      {userEstimates.map((estimate) => (
                        <Grid item xs={12} key={`${estimate.taskCategory}-${estimate.taskPriority}`}>
                          <Card variant="outlined">
                            <CardContent sx={{ p: 2, '&:last-child': { pb: 2 } }}>
                              <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: 1 }}>
                                <Typography variant="subtitle2">
                                  {estimate.taskCategory}
                                </Typography>
                                <Chip
                                  label={estimate.taskPriority}
                                  size="small"
                                  color={
                                    estimate.taskPriority === 'High' || estimate.taskPriority === 'Urgent'
                                      ? 'error'
                                      : 'default'
                                  }
                                />
                              </Box>

                              <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
                                <Box>
                                  <Typography variant="body2" color="text.secondary">
                                    Fit Score
                                  </Typography>
                                  <Typography variant="h6" color="primary" fontWeight="bold">
                                    {Math.round(estimate.fitScore)}%
                                  </Typography>
                                </Box>

                                <Box>
                                  <Typography variant="body2" color="text.secondary">
                                    Est. Time
                                  </Typography>
                                  <Typography variant="body1">
                                    {Math.round(estimate.estimatedCompletionTime)} hrs
                                  </Typography>
                                </Box>
                              </Box>

                              {estimate.performanceMetrics && (
                                <Box sx={{ mt: 1 }}>
                                  <Box sx={{ display: 'flex', justifyContent: 'space-between' }}>
                                    <Typography variant="caption">Completion Rate</Typography>
                                    <Typography variant="caption">
                                      {Math.round(estimate.performanceMetrics.completionRate)}%
                                    </Typography>
                                  </Box>
                                  <LinearProgress
                                    variant="determinate"
                                    value={estimate.performanceMetrics.completionRate}
                                    sx={{ mb: 1, height: 6, borderRadius: 3 }}
                                  />

                                  <Box sx={{ display: 'flex', justifyContent: 'space-between' }}>
                                    <Typography variant="caption">On-Time Rate</Typography>
                                    <Typography variant="caption">
                                      {Math.round(estimate.performanceMetrics.onTimeRate)}%
                                    </Typography>
                                  </Box>
                                  <LinearProgress
                                    variant="determinate"
                                    value={estimate.performanceMetrics.onTimeRate}
                                    sx={{ mb: 1, height: 6, borderRadius: 3 }}
                                  />

                                  <Box sx={{ display: 'flex', justifyContent: 'space-between' }}>
                                    <Typography variant="caption">Quality Score</Typography>
                                    <Typography variant="caption">
                                      {Math.round(estimate.performanceMetrics.qualityScore)}%
                                    </Typography>
                                  </Box>
                                  <LinearProgress
                                    variant="determinate"
                                    value={estimate.performanceMetrics.qualityScore}
                                    sx={{ height: 6, borderRadius: 3 }}
                                  />
                                </Box>
                              )}
                            </CardContent>
                          </Card>
                        </Grid>
                      ))}
                    </Grid>
                  </>
                )}
              </>
            ) : (
              <Box sx={{ display: 'flex', justifyContent: 'center', alignItems: 'center', height: '100%', flexDirection: 'column', p: 4 }}>
                <Person sx={{ fontSize: 60, color: 'text.secondary', mb: 2, opacity: 0.5 }} />
                <Typography variant="body1" color="text.secondary" align="center">
                  Select an employee from the list to view their performance profile and fit scores.
                </Typography>
              </Box>
            )}
          </Paper>
        </Grid>
      </Grid>
    </>
  );

  return (
    <Box>
      <Box sx={{ mb: 4 }}>
        <Typography variant="h4" component="h1" gutterBottom fontWeight={700}>
          General Estimating Knowledge (GEK) System
        </Typography>
        <Typography variant="body1" color="text.secondary" paragraph>
          Data-driven insights to help assign the right people to the right tasks based on performance history, attendance, and evaluations.
        </Typography>
      </Box>

      {/* Tabs */}
      <Paper sx={{ mb: 3 }}>
        <Tabs
          value={activeTab}
          onChange={handleTabChange}
          variant="fullWidth"
          indicatorColor="primary"
          textColor="primary"
        >
          {tabs.map((tab) => (
            <Tab
              key={tab.value}
              label={tab.label}
              icon={tab.icon}
              iconPosition="start"
              value={tab.value}
            />
          ))}
        </Tabs>
      </Paper>

      {/* Tab Content */}
      {renderTabContent()}
    </Box>
  );
};

export default GEKDashboard;
