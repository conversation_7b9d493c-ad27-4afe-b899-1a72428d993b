const express = require('express');
const router = express.Router();
const Task = require('../models/Task');
const User = require('../models/User');
const { authenticate, authorizeRoles } = require('../middleware/authmiddleware');
const mongoose = require('mongoose');
const {
  createTaskAssignedNotification,
  createTaskUpdateNotification
} = require('../controllers/notificationController');

// ==================== HR ROUTES ====================

// Utility route to make all existing tasks unassigned (for GEK system migration)
router.post('/migrate-to-gek', authenticate, authorizeRoles('hr'), async (req, res) => {
  try {
    // Update all tasks that are currently assigned to be unassigned
    const result = await Task.updateMany(
      {
        assignedTo: { $ne: null },
        status: { $in: ['Assigned', 'In Progress', 'Not Started'] }
      },
      {
        $set: {
          assignedTo: null,
          status: 'Unassigned',
          gekFitScore: 0,
          estimatedCompletionTime: 8
        }
      }
    );

    res.json({
      message: 'Tasks migrated to GEK system successfully',
      modifiedCount: result.modifiedCount,
      matchedCount: result.matchedCount
    });
  } catch (error) {
    console.error('Error migrating tasks to GEK system:', error);
    res.status(500).json({ message: 'Server error during migration' });
  }
});

// Create a new task (HR only)
router.post('/', authenticate, authorizeRoles('hr'), async (req, res) => {
  try {
    const {
      title,
      description,
      category,
      priority,
      deadline,
      assignedTo,
      visibleToManagers
    } = req.body;

    // Validate required fields (assignedTo and description are now optional)
    if (!title || !deadline) {
      return res.status(400).json({
        message: 'Missing required fields. Title and deadline are required.'
      });
    }

    let assigneeId = null;
    let gekFitScore = 0;
    let estimatedCompletionTime = 8; // Default 8 hours
    let taskStatus = 'Unassigned'; // Default status for unassigned tasks

    // If assignedTo is provided, validate and calculate GEK data
    if (assignedTo) {
      // Handle assignedTo as a single ID (not an array)
      assigneeId = Array.isArray(assignedTo) ? assignedTo[0] : assignedTo;

      // Validate assignee exists
      const assignee = await User.findById(assigneeId);

      if (!assignee) {
        return res.status(400).json({
          message: 'Invalid assignee. User not found.'
        });
      }

      taskStatus = 'Assigned';

      // Calculate GEK fit score for the assigned employee
      try {
        const { calculateFitScore } = require('../controllers/gekController');
        const fitScoreData = await calculateFitScore(
          assigneeId,
          category || 'General',
          priority || 'Medium'
        );

        gekFitScore = fitScoreData.fitScore || 0;
        estimatedCompletionTime = fitScoreData.estimatedCompletionTime || 8;
      } catch (gekError) {
        console.warn('Failed to calculate GEK fit score:', gekError.message);
        // Continue with task creation even if GEK calculation fails
      }
    }

    // Create new task
    const newTask = new Task({
      title,
      description: description || '', // Default to empty string if not provided
      category: category || 'General',
      priority: priority || 'Medium',
      deadline: new Date(deadline),
      createdBy: req.user.id,
      assignedTo: assigneeId,
      status: taskStatus,
      visibleToManagers: visibleToManagers || false,
      gekFitScore,
      estimatedCompletionTime
    });

    await newTask.save();

    // Populate creator and assignee details
    const populatedTask = await Task.findById(newTask._id)
      .populate('createdBy', 'name email')
      .populate('assignedTo', 'name email job department');

    // Create notification for the assignee only if task is assigned
    if (populatedTask.assignedTo) {
      await createTaskAssignedNotification(populatedTask);
    }

    res.status(201).json({
      message: 'Task created successfully',
      task: populatedTask,
      gekData: {
        fitScore: gekFitScore,
        estimatedCompletionTime
      }
    });
  } catch (error) {
    console.error('Error creating task:', error);
    res.status(500).json({ message: 'Server error' });
  }
});

// Get all tasks (HR only)
router.get('/hr', authenticate, authorizeRoles('hr'), async (req, res) => {
  try {
    const {
      status,
      category,
      priority,
      assignedTo,
      search,
      sortBy = 'deadline',
      sortOrder = 'asc',
      page = 1,
      limit = 10
    } = req.query;

    // Build query
    const query = { createdBy: req.user.id };

    if (status) query.status = status;
    if (category) query.category = category;
    if (priority) query.priority = priority;
    if (assignedTo) query.assignedTo = assignedTo;

    // Text search
    if (search) {
      // First, find users that match the search criteria
      const matchingUsers = await User.find({
        $or: [
          { name: { $regex: search, $options: 'i' } },
          { email: { $regex: search, $options: 'i' } },
          { job: { $regex: search, $options: 'i' } }
        ]
      }).select('_id');

      // Get the user IDs
      const userIds = matchingUsers.map(user => user._id);

      // Build the query to search in tasks
      query.$or = [
        { title: { $regex: search, $options: 'i' } },
        { description: { $regex: search, $options: 'i' } },
        { status: { $regex: search, $options: 'i' } },
        { priority: { $regex: search, $options: 'i' } },
        { category: { $regex: search, $options: 'i' } }
      ];

      // Add user IDs to the query if any were found
      if (userIds.length > 0) {
        query.$or.push({ assignedTo: { $in: userIds } });
      }
    }

    // Build sort
    const sort = {};
    sort[sortBy] = sortOrder === 'desc' ? -1 : 1;

    // Calculate pagination
    const skip = (parseInt(page) - 1) * parseInt(limit);

    // Execute query with pagination
    const tasks = await Task.find(query)
      .sort(sort)
      .skip(skip)
      .limit(parseInt(limit))
      .populate('assignedTo', 'name email job')
      .populate('createdBy', 'name email');

    // Get total count for pagination
    const totalTasks = await Task.countDocuments(query);

    res.json({
      tasks,
      pagination: {
        total: totalTasks,
        page: parseInt(page),
        limit: parseInt(limit),
        pages: Math.ceil(totalTasks / parseInt(limit))
      }
    });
  } catch (error) {
    console.error('Error fetching tasks:', error);
    res.status(500).json({ message: 'Server error' });
  }
});

// Get task by ID (HR and assigned users)
router.get('/:id', authenticate, async (req, res) => {
  try {
    const taskId = req.params.id;

    // Validate task ID
    if (!mongoose.Types.ObjectId.isValid(taskId)) {
      return res.status(400).json({ message: 'Invalid task ID' });
    }

    const task = await Task.findById(taskId)
      .populate('assignedTo', 'name email job')
      .populate('createdBy', 'name email')
      .populate('updates.userId', 'name email role');

    if (!task) {
      return res.status(404).json({ message: 'Task not found' });
    }

    // Check if user is authorized to view this task
    const isHR = req.user.role === 'hr';
    const isCreator = task.createdBy._id.toString() === req.user.id;
    const isAssignee = task.assignedTo._id.toString() === req.user.id;
    const isManager = req.user.role === 'hr' && task.visibleToManagers;

    if (!isHR && !isCreator && !isAssignee && !isManager) {
      return res.status(403).json({ message: 'You are not authorized to view this task' });
    }

    res.json(task);
  } catch (error) {
    console.error('Error fetching task:', error);
    res.status(500).json({ message: 'Server error' });
  }
});

// Update task (HR only)
router.put('/:id', authenticate, authorizeRoles('hr'), async (req, res) => {
  try {
    const taskId = req.params.id;
    const {
      title,
      description,
      category,
      priority,
      deadline,
      assignedTo,
      status,
      feedback,
      visibleToManagers
    } = req.body;

    // Validate task ID
    if (!mongoose.Types.ObjectId.isValid(taskId)) {
      return res.status(400).json({ message: 'Invalid task ID' });
    }

    // Find task
    const task = await Task.findById(taskId);

    if (!task) {
      return res.status(404).json({ message: 'Task not found' });
    }

    // Check if user is the creator of the task
    if (task.createdBy.toString() !== req.user.id) {
      return res.status(403).json({ message: 'You are not authorized to update this task' });
    }

    // Update task fields
    if (title) task.title = title;
    if (description) task.description = description;
    if (category) task.category = category;
    if (priority) task.priority = priority;
    if (deadline) task.deadline = new Date(deadline);
    if (status) {
      task.status = status;

      // If marking as completed, set completedAt
      if (status === 'Completed' && task.status !== 'Completed') {
        task.completedAt = new Date();
        task.progress = 100;
      }

      // If marking as needs revision, add update
      if (status === 'Needs Revision') {
        task.updates.push({
          userId: req.user.id,
          message: 'Task needs revision',
          timestamp: new Date()
        });
      }
    }
    if (feedback !== undefined) task.feedback = feedback;
    if (visibleToManagers !== undefined) task.visibleToManagers = visibleToManagers;

    // Handle assignee updates
    if (assignedTo !== undefined) {
      if (assignedTo === null || assignedTo === '') {
        // Unassign the task
        task.assignedTo = null;
        task.status = 'Unassigned';
      } else {
        // Handle assignedTo as a single ID (not an array)
        const assigneeId = Array.isArray(assignedTo) ? assignedTo[0] : assignedTo;

        // Validate assignee exists
        const assignee = await User.findById(assigneeId);

        if (!assignee) {
          return res.status(400).json({
            message: 'Invalid assignee. User not found.'
          });
        }

        task.assignedTo = assigneeId;
        task.status = 'Assigned';
      }
    }

    // Add update record
    task.updates.push({
      userId: req.user.id,
      message: 'Task updated by HR',
      timestamp: new Date()
    });

    await task.save();

    // Populate creator and assignee details
    const updatedTask = await Task.findById(task._id)
      .populate('assignedTo', 'name email job')
      .populate('createdBy', 'name email')
      .populate('updates.userId', 'name email role');

    // Create notification for task update
    const updateMessage = status === 'Needs Revision'
      ? 'Task needs revision'
      : 'Task details have been updated';
    await createTaskUpdateNotification(updatedTask, updateMessage);

    res.json({
      message: 'Task updated successfully',
      task: updatedTask
    });
  } catch (error) {
    console.error('Error updating task:', error);
    res.status(500).json({ message: 'Server error' });
  }
});

// Delete task (HR only)
router.delete('/:id', authenticate, authorizeRoles('hr'), async (req, res) => {
  try {
    const taskId = req.params.id;

    // Validate task ID
    if (!mongoose.Types.ObjectId.isValid(taskId)) {
      return res.status(400).json({ message: 'Invalid task ID' });
    }

    // Find task
    const task = await Task.findById(taskId);

    if (!task) {
      return res.status(404).json({ message: 'Task not found' });
    }

    // Check if user is the creator of the task
    if (task.createdBy.toString() !== req.user.id) {
      return res.status(403).json({ message: 'You are not authorized to delete this task' });
    }

    await Task.findByIdAndDelete(taskId);

    res.json({ message: 'Task deleted successfully' });
  } catch (error) {
    console.error('Error deleting task:', error);
    res.status(500).json({ message: 'Server error' });
  }
});

// ==================== USER ROUTES ====================

// Get tasks assigned to user
router.get('/user/assigned', authenticate, async (req, res) => {
  try {
    const {
      status,
      category,
      priority,
      search,
      sortBy = 'deadline',
      sortOrder = 'asc',
      page = 1,
      limit = 10
    } = req.query;

    // Build query
    const query = { assignedTo: req.user.id };

    if (status) query.status = status;
    if (category) query.category = category;
    if (priority) query.priority = priority;

    // Text search
    if (search) {
      query.$or = [
        { title: { $regex: search, $options: 'i' } },
        { description: { $regex: search, $options: 'i' } },
        { status: { $regex: search, $options: 'i' } },
        { priority: { $regex: search, $options: 'i' } },
        { category: { $regex: search, $options: 'i' } }
      ];
    }

    // Build sort
    const sort = {};
    sort[sortBy] = sortOrder === 'desc' ? -1 : 1;

    // Calculate pagination
    const skip = (parseInt(page) - 1) * parseInt(limit);

    // Execute query with pagination
    const tasks = await Task.find(query)
      .sort(sort)
      .skip(skip)
      .limit(parseInt(limit))
      .populate('assignedTo', 'name email job')
      .populate('createdBy', 'name email');

    // Get total count for pagination
    const totalTasks = await Task.countDocuments(query);

    res.json({
      tasks,
      pagination: {
        total: totalTasks,
        page: parseInt(page),
        limit: parseInt(limit),
        pages: Math.ceil(totalTasks / parseInt(limit))
      }
    });
  } catch (error) {
    console.error('Error fetching assigned tasks:', error);
    res.status(500).json({ message: 'Server error' });
  }
});

// Helper function for updating task status
const updateTaskStatus = async (req, res) => {
  try {
    const taskId = req.params.id;
    const { status, progress, message } = req.body;

    console.log('Updating task status:', {
      taskId,
      status,
      progress,
      message,
      userId: req.user.id,
      userRole: req.user.role
    });

    // Validate task ID
    if (!mongoose.Types.ObjectId.isValid(taskId)) {
      console.log('Invalid task ID format:', taskId);
      return res.status(400).json({
        message: 'Invalid task ID format',
        details: 'The provided ID is not a valid MongoDB ObjectId'
      });
    }

    // Find task
    const task = await Task.findById(taskId);

    if (!task) {
      console.log('Task not found with ID:', taskId);
      return res.status(404).json({
        message: 'Task not found',
        details: `No task exists with ID: ${taskId}`
      });
    }

    console.log('Task found:', {
      taskId: task._id,
      title: task.title,
      assignedTo: task.assignedTo,
      currentStatus: task.status
    });

    // Check if user is assigned to this task
    const isAssigned = task.assignedTo.toString() === req.user.id;
    const isHR = req.user.role === 'hr';

    console.log('Authorization check:', {
      userId: req.user.id,
      isAssigned,
      isHR,
      assignedUser: task.assignedTo.toString()
    });

    if (!isAssigned && !isHR) {
      return res.status(403).json({
        message: 'You are not assigned to this task',
        details: 'Only users assigned to this task or HR can update its status'
      });
    }

    // Validate status
    if (status && !['In Progress', 'Completed', 'Assigned'].includes(status)) {
      return res.status(400).json({
        message: 'Invalid status value',
        details: 'Users can only set status to "In Progress", "Assigned", or "Completed".',
        receivedStatus: status,
        allowedValues: ['In Progress', 'Completed', 'Assigned']
      });
    }

    // Update task fields
    if (status) {
      task.status = status;

      // If marking as completed, set completedAt
      if (status === 'Completed' && task.status !== 'Completed') {
        task.completedAt = new Date();
        task.progress = 100;
      }
    }

    // Update progress if provided
    if (progress !== undefined && progress >= 0 && progress <= 100) {
      task.progress = progress;
    }

    // Add update record
    task.updates.push({
      userId: req.user.id,
      message: message || `Task status updated to ${status || task.status}`,
      timestamp: new Date()
    });

    await task.save();
    console.log('Task updated successfully');

    // Populate creator and assignee details
    const updatedTask = await Task.findById(task._id)
      .populate('assignedTo', 'name email job')
      .populate('createdBy', 'name email')
      .populate('updates.userId', 'name email role');

    // Create notification for task status update
    // If the user updating is HR, notify the assignee
    // If the user updating is the assignee, notify the HR who created the task
    const notifyUserId = req.user.role === 'hr' ? task.assignedTo : task.createdBy;
    const updateMessage = message || `Task status updated to ${status || task.status}`;
    await createTaskUpdateNotification({
      ...updatedTask.toObject(),
      assignedTo: notifyUserId
    }, updateMessage);

    res.json({
      message: 'Task status updated successfully',
      task: updatedTask
    });
  } catch (error) {
    console.error('Error updating task status:', error);
    res.status(500).json({
      message: 'Server error',
      details: error.message,
      stack: process.env.NODE_ENV === 'development' ? error.stack : undefined
    });
  }
};

// Update task status (User) - PATCH method
router.patch('/:id/status', authenticate, updateTaskStatus);

// Update task status (User) - POST method (alternative for clients that don't support PATCH)
router.post('/:id/status', authenticate, updateTaskStatus);

// Add comment/update to task
router.post('/:id/updates', authenticate, async (req, res) => {
  try {
    const taskId = req.params.id;
    const { message } = req.body;

    if (!message) {
      return res.status(400).json({ message: 'Message is required' });
    }

    // Validate task ID
    if (!mongoose.Types.ObjectId.isValid(taskId)) {
      return res.status(400).json({ message: 'Invalid task ID' });
    }

    // Find task
    const task = await Task.findById(taskId);

    if (!task) {
      return res.status(404).json({ message: 'Task not found' });
    }

    // Check if user is authorized to add updates
    const isHR = req.user.role === 'hr';
    const isCreator = task.createdBy.toString() === req.user.id;
    const isAssignee = task.assignedTo.some(userId => userId.toString() === req.user.id);

    if (!isHR && !isCreator && !isAssignee) {
      return res.status(403).json({ message: 'You are not authorized to update this task' });
    }

    // Add update
    task.updates.push({
      userId: req.user.id,
      message,
      timestamp: new Date()
    });

    await task.save();

    // Populate creator and assignee details
    const updatedTask = await Task.findById(task._id)
      .populate('assignedTo', 'name email job')
      .populate('createdBy', 'name email')
      .populate('updates.userId', 'name email role');

    res.json({
      message: 'Update added successfully',
      task: updatedTask
    });
  } catch (error) {
    console.error('Error adding update to task:', error);
    res.status(500).json({ message: 'Server error' });
  }
});

module.exports = router;
