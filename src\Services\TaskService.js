import api from './ApiService';

/**
 * Task Service
 * Handles all task-related API calls
 */
class TaskService {
  /**
   * Get HR tasks with optional filters
   * @param {Object} filters - Filter options
   * @returns {Promise} API response with tasks
   */
  static async getHRTasks(filters = {}) {
    try {
      const params = new URLSearchParams();
      
      // Add filters to params
      Object.keys(filters).forEach(key => {
        if (filters[key] && filters[key] !== '') {
          params.append(key, filters[key]);
        }
      });

      const response = await api.get(`/api/tasks/hr?${params.toString()}`);
      return response.data;
    } catch (error) {
      console.error('Error getting HR tasks:', error);
      throw error;
    }
  }

  /**
   * Get tasks assigned to current user
   * @param {Object} filters - Filter options
   * @returns {Promise} API response with tasks
   */
  static async getUserTasks(filters = {}) {
    try {
      const params = new URLSearchParams();
      
      // Add filters to params
      Object.keys(filters).forEach(key => {
        if (filters[key] && filters[key] !== '') {
          params.append(key, filters[key]);
        }
      });

      const response = await api.get(`/api/tasks/user/assigned?${params.toString()}`);
      return response.data;
    } catch (error) {
      console.error('Error getting user tasks:', error);
      throw error;
    }
  }

  /**
   * Get a specific task by ID
   * @param {string} taskId - Task ID
   * @returns {Promise} API response with task details
   */
  static async getTaskById(taskId) {
    try {
      const response = await api.get(`/api/tasks/${taskId}`);
      return response.data;
    } catch (error) {
      console.error('Error getting task by ID:', error);
      throw error;
    }
  }

  /**
   * Create a new task
   * @param {Object} taskData - Task data
   * @returns {Promise} API response with created task
   */
  static async createTask(taskData) {
    try {
      const response = await api.post('/api/tasks', taskData);
      return response.data;
    } catch (error) {
      console.error('Error creating task:', error);
      throw error;
    }
  }

  /**
   * Update an existing task
   * @param {string} taskId - Task ID
   * @param {Object} taskData - Updated task data
   * @returns {Promise} API response with updated task
   */
  static async updateTask(taskId, taskData) {
    try {
      const response = await api.put(`/api/tasks/${taskId}`, taskData);
      return response.data;
    } catch (error) {
      console.error('Error updating task:', error);
      throw error;
    }
  }

  /**
   * Delete a task
   * @param {string} taskId - Task ID
   * @returns {Promise} API response
   */
  static async deleteTask(taskId) {
    try {
      const response = await api.delete(`/api/tasks/${taskId}`);
      return response.data;
    } catch (error) {
      console.error('Error deleting task:', error);
      throw error;
    }
  }

  /**
   * Update task status
   * @param {string} taskId - Task ID
   * @param {Object} statusData - Status update data
   * @returns {Promise} API response with updated task
   */
  static async updateTaskStatus(taskId, statusData) {
    try {
      const response = await api.patch(`/api/tasks/${taskId}/status`, statusData);
      return response.data;
    } catch (error) {
      console.error('Error updating task status:', error);
      throw error;
    }
  }

  /**
   * Add an update/comment to a task
   * @param {string} taskId - Task ID
   * @param {string} message - Update message
   * @returns {Promise} API response with updated task
   */
  static async addTaskUpdate(taskId, message) {
    try {
      const response = await api.post(`/api/tasks/${taskId}/updates`, { message });
      return response.data;
    } catch (error) {
      console.error('Error adding task update:', error);
      throw error;
    }
  }

  /**
   * Assign a task to an employee
   * @param {string} taskId - Task ID
   * @param {string} employeeId - Employee ID
   * @param {string} reason - Assignment reason
   * @returns {Promise} API response with updated task
   */
  static async assignTask(taskId, employeeId, reason = '') {
    try {
      const response = await api.put(`/api/tasks/${taskId}`, {
        assignedTo: employeeId,
        status: 'Assigned',
        assignmentReason: reason
      });
      return response.data;
    } catch (error) {
      console.error('Error assigning task:', error);
      throw error;
    }
  }

  /**
   * Unassign a task (make it available for GEK assignment)
   * @param {string} taskId - Task ID
   * @returns {Promise} API response with updated task
   */
  static async unassignTask(taskId) {
    try {
      const response = await api.put(`/api/tasks/${taskId}`, {
        assignedTo: null,
        status: 'Unassigned'
      });
      return response.data;
    } catch (error) {
      console.error('Error unassigning task:', error);
      throw error;
    }
  }

  /**
   * Get task statistics
   * @returns {Promise} API response with task statistics
   */
  static async getTaskStatistics() {
    try {
      const response = await api.get('/api/tasks/statistics');
      return response.data;
    } catch (error) {
      console.error('Error getting task statistics:', error);
      
      // Return mock statistics as fallback
      return {
        total: 0,
        unassigned: 0,
        assigned: 0,
        inProgress: 0,
        completed: 0,
        overdue: 0
      };
    }
  }

  /**
   * Migrate existing tasks to GEK system
   * @returns {Promise} API response with migration results
   */
  static async migrateTasksToGEK() {
    try {
      const response = await api.post('/api/tasks/migrate-to-gek');
      return response.data;
    } catch (error) {
      console.error('Error migrating tasks to GEK:', error);
      throw error;
    }
  }

  /**
   * Search tasks with advanced filters
   * @param {Object} searchParams - Search parameters
   * @returns {Promise} API response with filtered tasks
   */
  static async searchTasks(searchParams) {
    try {
      const params = new URLSearchParams();
      
      // Add search parameters
      Object.keys(searchParams).forEach(key => {
        if (searchParams[key] && searchParams[key] !== '') {
          params.append(key, searchParams[key]);
        }
      });

      const response = await api.get(`/api/tasks/search?${params.toString()}`);
      return response.data;
    } catch (error) {
      console.error('Error searching tasks:', error);
      throw error;
    }
  }

  /**
   * Get task performance analytics
   * @param {Object} filters - Filter options
   * @returns {Promise} API response with analytics
   */
  static async getTaskAnalytics(filters = {}) {
    try {
      const params = new URLSearchParams();
      
      Object.keys(filters).forEach(key => {
        if (filters[key] && filters[key] !== '') {
          params.append(key, filters[key]);
        }
      });

      const response = await api.get(`/api/tasks/analytics?${params.toString()}`);
      return response.data;
    } catch (error) {
      console.error('Error getting task analytics:', error);
      
      // Return mock analytics as fallback
      return {
        averageCompletionTime: 8.5,
        onTimeDeliveryRate: 0.85,
        tasksByCategory: {},
        tasksByPriority: {},
        completionTrends: []
      };
    }
  }
}

export default TaskService;
